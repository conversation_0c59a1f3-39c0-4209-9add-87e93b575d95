import React, { useState, useEffect } from 'react';
import { Project, ChatMessage, MessageRole, ProjectStatus } from '../types';
import { ChatWindow } from './ChatWindow';
import { MessageInput } from './MessageInput';
import { 
  createProjectChatSession, 
  handleProjectChatMessage, 
  getProjectSuggestedQuestions,
  ProjectChatSession 
} from '../services/chatService';
import { getProjectById } from '../services/projectService';

interface ProjectChatProps {
  projectId: string;
  embedded?: boolean; // Whether this is embedded in an iframe
  onError?: (error: string) => void;
}

export const ProjectChat: React.FC<ProjectChatProps> = ({ 
  projectId, 
  embedded = false,
  onError 
}) => {
  const [project, setProject] = useState<Project | null>(null);
  const [session, setSession] = useState<ProjectChatSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAnswering, setIsAnswering] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([]);

  // Initialize chat session
  useEffect(() => {
    const initializeChat = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load project data
        const projectData = await getProjectById(projectId);
        if (!projectData) {
          throw new Error('Project not found');
        }

        if (projectData.status !== ProjectStatus.READY) {
          throw new Error('This chatbot is not yet ready. Please check back later.');
        }

        setProject(projectData);

        // Create chat session
        const chatSession = await createProjectChatSession(projectId, projectData);
        setSession(chatSession);

        // Load suggested questions
        const questions = getProjectSuggestedQuestions(projectData);
        setSuggestedQuestions(questions);

      } catch (err) {
        console.error('Error initializing chat:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize chat';
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (projectId) {
      initializeChat();
    }
  }, [projectId, onError]);

  const handleSendMessage = async (userMessage: string) => {
    if (!session || !session.isReady) {
      return;
    }

    setIsAnswering(true);

    try {
      const result = await handleProjectChatMessage(session, userMessage);
      setSession(result.updatedSession);
    } catch (err) {
      console.error('Error sending message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      
      // Add error message to chat
      const errorChatMessage: ChatMessage = {
        role: MessageRole.SYSTEM,
        content: `Error: ${errorMessage}`,
        timestamp: Date.now(),
      };
      
      if (session) {
        setSession({
          ...session,
          messages: [...session.messages, errorChatMessage],
        });
      }
    } finally {
      setIsAnswering(false);
    }
  };

  const handleSuggestedQuestionClick = (question: string) => {
    handleSendMessage(question);
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-secondary-light dark:bg-slate-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-text-secondary-light dark:text-slate-400">Loading chat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center bg-secondary-light dark:bg-slate-900 p-6">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-2">
            Chat Unavailable
          </h3>
          <p className="text-text-secondary-light dark:text-slate-400 text-sm">
            {error}
          </p>
        </div>
      </div>
    );
  }

  if (!session || !project) {
    return (
      <div className="h-full flex items-center justify-center bg-secondary-light dark:bg-slate-900">
        <p className="text-text-secondary-light dark:text-slate-400">Failed to load chat session</p>
      </div>
    );
  }

  const themeColor = project.config.themeColor || '#4f46e5';

  return (
    <div 
      className="h-full flex flex-col bg-secondary-light dark:bg-slate-900"
      style={{ 
        '--theme-color': themeColor,
        '--theme-color-hover': `${themeColor}dd`,
      } as React.CSSProperties}
    >
      {/* Header */}
      {!embedded && (
        <header 
          className="flex-shrink-0 px-4 py-3 text-white"
          style={{ backgroundColor: themeColor }}
        >
          <div className="flex items-center gap-3">
            {project.config.headerLogo && (
              <img 
                src={project.config.headerLogo} 
                alt="Logo" 
                className="w-8 h-8 rounded"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <div>
              <h1 className="font-semibold">{project.name}</h1>
              <p className="text-xs opacity-90">AI Assistant</p>
            </div>
          </div>
        </header>
      )}

      {/* Chat Area */}
      <div className="flex-grow flex flex-col bg-panel-light dark:bg-slate-800 overflow-hidden">
        <ChatWindow messages={session.messages} isLoading={isAnswering} />
        
        {/* Suggested Questions */}
        {suggestedQuestions.length > 0 && session.messages.length <= 1 && (
          <div className="flex-shrink-0 p-4 border-t border-border-light dark:border-slate-700">
            <p className="text-sm text-text-secondary-light dark:text-slate-400 mb-2">
              Suggested questions:
            </p>
            <div className="space-y-2">
              {suggestedQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestedQuestionClick(question)}
                  disabled={isAnswering}
                  className="block w-full text-left px-3 py-2 text-sm bg-secondary-light dark:bg-slate-700 hover:bg-border-light dark:hover:bg-slate-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ 
                    borderLeft: `3px solid ${themeColor}`,
                  }}
                >
                  {question}
                </button>
              ))}
            </div>
          </div>
        )}
        
        <MessageInput 
          onSendMessage={handleSendMessage} 
          disabled={!session.isReady || isAnswering} 
        />
      </div>
    </div>
  );
};
