(function() {
    'use strict';
    
    // Configuration from window.eComQnAConfig
    const config = window.eComQnAConfig || {};
    const projectId = config.projectId;
    const themeColor = config.themeColor || '#4f46e5';
    const position = config.position || 'bottom-right';
    const baseUrl = config.baseUrl || window.location.origin;
    
    if (!projectId) {
        console.error('eComQnA: projectId is required in window.eComQnAConfig');
        return;
    }
    
    // Prevent multiple initializations
    if (window.eComQnAInitialized) {
        return;
    }
    window.eComQnAInitialized = true;
    
    // Create chat widget
    function createChatWidget() {
        // Create container
        const container = document.createElement('div');
        container.id = 'ecomqna-widget';
        container.style.cssText = `
            position: fixed;
            z-index: 2147483647;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        // Position the container
        switch (position) {
            case 'bottom-left':
                container.style.bottom = '20px';
                container.style.left = '20px';
                break;
            case 'bottom-right':
            default:
                container.style.bottom = '20px';
                container.style.right = '20px';
                break;
        }
        
        // Create chat button
        const chatButton = document.createElement('button');
        chatButton.id = 'ecomqna-chat-button';
        chatButton.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
        `;
        chatButton.style.cssText = `
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            background-color: ${themeColor};
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        // Create chat iframe (initially hidden)
        const chatIframe = document.createElement('iframe');
        chatIframe.id = 'ecomqna-chat-iframe';
        chatIframe.src = `${baseUrl}/#/chat/${projectId}`;
        chatIframe.style.cssText = `
            width: 400px;
            height: 600px;
            border: none;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            background: white;
            position: absolute;
            bottom: 80px;
            right: 0;
            display: none;
            transition: all 0.3s ease;
        `;
        
        // Adjust iframe position for bottom-left
        if (position === 'bottom-left') {
            chatIframe.style.right = 'auto';
            chatIframe.style.left = '0';
        }
        
        // Create close button for iframe
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '×';
        closeButton.style.cssText = `
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            border: none;
            background: rgba(0, 0, 0, 0.1);
            color: #666;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            display: none;
            z-index: 1;
        `;
        
        // State management
        let isOpen = false;
        
        // Toggle chat
        function toggleChat() {
            isOpen = !isOpen;
            if (isOpen) {
                chatIframe.style.display = 'block';
                closeButton.style.display = 'block';
                chatButton.style.transform = 'rotate(180deg)';
                
                // Focus iframe for accessibility
                setTimeout(() => {
                    chatIframe.focus();
                }, 100);
            } else {
                chatIframe.style.display = 'none';
                closeButton.style.display = 'none';
                chatButton.style.transform = 'rotate(0deg)';
            }
        }
        
        // Event listeners
        chatButton.addEventListener('click', toggleChat);
        closeButton.addEventListener('click', toggleChat);
        
        // Hover effects
        chatButton.addEventListener('mouseenter', function() {
            this.style.transform = isOpen ? 'rotate(180deg) scale(1.1)' : 'scale(1.1)';
        });
        
        chatButton.addEventListener('mouseleave', function() {
            this.style.transform = isOpen ? 'rotate(180deg)' : 'scale(1)';
        });
        
        // Close on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isOpen) {
                toggleChat();
            }
        });
        
        // Close when clicking outside
        document.addEventListener('click', function(e) {
            if (isOpen && !container.contains(e.target)) {
                toggleChat();
            }
        });
        
        // Responsive adjustments
        function adjustForMobile() {
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                chatIframe.style.width = '100vw';
                chatIframe.style.height = '100vh';
                chatIframe.style.position = 'fixed';
                chatIframe.style.top = '0';
                chatIframe.style.left = '0';
                chatIframe.style.right = 'auto';
                chatIframe.style.bottom = 'auto';
                chatIframe.style.borderRadius = '0';
            } else {
                chatIframe.style.width = '400px';
                chatIframe.style.height = '600px';
                chatIframe.style.position = 'absolute';
                chatIframe.style.top = 'auto';
                chatIframe.style.bottom = '80px';
                chatIframe.style.borderRadius = '12px';
                
                if (position === 'bottom-left') {
                    chatIframe.style.left = '0';
                    chatIframe.style.right = 'auto';
                } else {
                    chatIframe.style.right = '0';
                    chatIframe.style.left = 'auto';
                }
            }
        }
        
        // Listen for window resize
        window.addEventListener('resize', adjustForMobile);
        adjustForMobile(); // Initial call
        
        // Assemble widget
        container.appendChild(chatButton);
        container.appendChild(chatIframe);
        container.appendChild(closeButton);
        
        return container;
    }
    
    // Initialize when DOM is ready
    function init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        // Check if widget already exists
        if (document.getElementById('ecomqna-widget')) {
            return;
        }
        
        const widget = createChatWidget();
        document.body.appendChild(widget);
        
        // Add some basic CSS for better integration
        const style = document.createElement('style');
        style.textContent = `
            #ecomqna-widget * {
                box-sizing: border-box;
            }
            
            #ecomqna-chat-button:hover {
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
            }
            
            #ecomqna-chat-iframe {
                animation: ecomqna-slide-up 0.3s ease-out;
            }
            
            @keyframes ecomqna-slide-up {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @media (max-width: 768px) {
                #ecomqna-chat-iframe {
                    animation: ecomqna-slide-in 0.3s ease-out;
                }
                
                @keyframes ecomqna-slide-in {
                    from {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
            }
        `;
        document.head.appendChild(style);
        
        console.log('eComQnA chat widget initialized');
    }
    
    // Start initialization
    init();
    
    // Expose API for programmatic control
    window.eComQnA = {
        open: function() {
            const button = document.getElementById('ecomqna-chat-button');
            if (button) button.click();
        },
        close: function() {
            const iframe = document.getElementById('ecomqna-chat-iframe');
            const closeBtn = document.querySelector('#ecomqna-widget button:last-child');
            if (iframe && iframe.style.display !== 'none' && closeBtn) {
                closeBtn.click();
            }
        },
        toggle: function() {
            const button = document.getElementById('ecomqna-chat-button');
            if (button) button.click();
        }
    };
})();
