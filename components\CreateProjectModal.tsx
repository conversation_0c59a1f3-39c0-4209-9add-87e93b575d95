import React, { useState, useEffect } from 'react';
import { validateProjectName, validateWebsiteUrl } from '../services/projectService';

interface CreateProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (name: string, websiteUrl?: string) => Promise<void>;
}

export const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [name, setName] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [nameError, setNameError] = useState<string | null>(null);
  const [urlError, setUrlError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setName('');
      setWebsiteUrl('');
      setNameError(null);
      setUrlError(null);
      setSubmitError(null);
      setIsSubmitting(false);
    }
  }, [isOpen]);

  // Validate name on change
  useEffect(() => {
    if (name) {
      const error = validateProjectName(name);
      setNameError(error);
    } else {
      setNameError(null);
    }
  }, [name]);

  // Validate URL on change
  useEffect(() => {
    if (websiteUrl) {
      const error = validateWebsiteUrl(websiteUrl);
      setUrlError(error);
    } else {
      setUrlError(null);
    }
  }, [websiteUrl]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Final validation
    const nameValidationError = validateProjectName(name);
    const urlValidationError = validateWebsiteUrl(websiteUrl);
    
    if (nameValidationError) {
      setNameError(nameValidationError);
      return;
    }
    
    if (urlValidationError) {
      setUrlError(urlValidationError);
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      await onSubmit(name, websiteUrl || undefined);
      onClose();
    } catch (error) {
      console.error('Error creating project:', error);
      setSubmitError(error instanceof Error ? error.message : 'Failed to create project');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const isFormValid = name.trim() && !nameError && !urlError;

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-md">
        {/* Header */}
        <div className="px-6 py-4 border-b border-border-light dark:border-slate-700">
          <h2 className="text-xl font-semibold text-text-light dark:text-slate-200">
            Create New Project
          </h2>
          <p className="text-sm text-text-secondary-light dark:text-slate-400 mt-1">
            Set up a new chatbot project for your website
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* Project Name */}
          <div className="mb-4">
            <label
              htmlFor="project-name"
              className="block text-sm font-medium text-text-light dark:text-slate-200 mb-2"
            >
              Project Name *
            </label>
            <input
              id="project-name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="My E-commerce Store"
              maxLength={50}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:outline-none transition-colors ${
                nameError
                  ? 'border-red-500 dark:border-red-400'
                  : 'border-border-light dark:border-slate-600'
              } bg-white dark:bg-slate-700 text-text-light dark:text-slate-200 placeholder:text-text-secondary-light dark:placeholder:text-slate-400`}
              disabled={isSubmitting}
            />
            {nameError && (
              <p className="text-red-500 dark:text-red-400 text-sm mt-1">{nameError}</p>
            )}
            <p className="text-xs text-text-secondary-light dark:text-slate-400 mt-1">
              {name.length}/50 characters
            </p>
          </div>

          {/* Website URL */}
          <div className="mb-6">
            <label
              htmlFor="website-url"
              className="block text-sm font-medium text-text-light dark:text-slate-200 mb-2"
            >
              Website URL (Optional)
            </label>
            <input
              id="website-url"
              type="url"
              value={websiteUrl}
              onChange={(e) => setWebsiteUrl(e.target.value)}
              placeholder="https://example.com"
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:outline-none transition-colors ${
                urlError
                  ? 'border-red-500 dark:border-red-400'
                  : 'border-border-light dark:border-slate-600'
              } bg-white dark:bg-slate-700 text-text-light dark:text-slate-200 placeholder:text-text-secondary-light dark:placeholder:text-slate-400`}
              disabled={isSubmitting}
            />
            {urlError && (
              <p className="text-red-500 dark:text-red-400 text-sm mt-1">{urlError}</p>
            )}
            <p className="text-xs text-text-secondary-light dark:text-slate-400 mt-1">
              This will be used for reference and embed code generation
            </p>
          </div>

          {/* Submit Error */}
          {submitError && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg">
              <p className="text-red-700 dark:text-red-400 text-sm">{submitError}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end gap-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-text-secondary-light dark:text-slate-400 hover:text-text-light dark:hover:text-slate-200 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!isFormValid || isSubmitting}
              className="px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isSubmitting && (
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
              )}
              {isSubmitting ? 'Creating...' : 'Create Project'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
