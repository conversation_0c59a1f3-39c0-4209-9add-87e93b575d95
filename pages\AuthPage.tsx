import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { LogoIcon, MoonIcon, SunIcon } from '../components/Icons';

type AuthView = 'signin' | 'signup' | 'forgot';

interface AuthPageProps {
  initialRoute: string;
}

const ThemeToggleButton: React.FC<{ className?: string }> = ({ className }) => {
    const { theme, toggleTheme } = useAuth();
    return (
        <button
            onClick={toggleTheme}
            className={`p-2 rounded-full text-text-secondary-light dark:text-text-secondary-dark hover:bg-black/5 dark:hover:bg-white/10 transition-colors ${className}`}
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
        >
            {theme === 'light' ? <MoonIcon className="w-5 h-5" /> : <SunIcon className="w-5 h-5" />}
        </button>
    );
};


export const AuthPage: React.FC<AuthPageProps> = ({ initialRoute }) => {
  const [view, setView] = useState<AuthView>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '', color: ''});
  const { signin, signup, resetPassword } = useAuth();

  useEffect(() => {
    switch (initialRoute) {
      case '#/signup':
        setView('signup');
        break;
      case '#/forgot-password':
        setView('forgot');
        break;
      default:
        setView('signin');
        break;
    }
    setError('');
    setMessage('');
    
    const rememberedEmail = localStorage.getItem('rememberedEmail');
    if (initialRoute === '#/signin' && rememberedEmail) {
      setEmail(rememberedEmail);
      setRememberMe(true);
    } else {
      setEmail('');
      setRememberMe(false);
    }

    setPassword('');
    setConfirmPassword('');
    setPasswordStrength({ score: 0, label: '', color: ''});
  }, [initialRoute]);

  const checkPasswordStrength = (pass: string) => {
    let score = 0;
    if (!pass) {
      setPasswordStrength({ score: 0, label: '', color: ''});
      return;
    }
    if (pass.length >= 8) score++;
    if (pass.match(/[a-z]/)) score++;
    if (pass.match(/[A-Z]/)) score++;
    if (pass.match(/[0-9]/)) score++;
    if (pass.match(/[^a-zA-Z0-9]/)) score++;

    let label = 'Weak';
    let color = 'bg-red-500';
    if (score > 4) {
      label = 'Strong';
      color = 'bg-green-500';
    } else if (score > 2) {
      label = 'Medium';
      color = 'bg-yellow-500';
    }
    setPasswordStrength({ score, label, color });
  };

  const handleAuthAction = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');
    
    if (view === 'signup' && password !== confirmPassword) {
      setError('Passwords do not match.');
      setLoading(false);
      return;
    }

    try {
      if (view === 'signin') {
        await signin(email, password);
        if (rememberMe) {
          localStorage.setItem('rememberedEmail', email);
        } else {
          localStorage.removeItem('rememberedEmail');
        }
      } else if (view === 'signup') {
        await signup(email, password);
      } else if (view === 'forgot') {
        await resetPassword(email);
        setMessage('Password reset email sent! Please check your inbox.');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setLoading(false);
    }
  };
  
  const getTitle = () => {
      switch(view) {
          case 'signin': return 'Secure Access';
          case 'signup': return 'Join the Future';
          case 'forgot': return 'Reset Your Key';
      }
  }

  const getButtonText = () => {
      if (loading) return 'Processing...';
      switch(view) {
          case 'signin': return 'Sign In';
          case 'signup': return 'Create Account';
          case 'forgot': return 'Send Reset Link';
      }
  }

  const navigateTo = (e: React.MouseEvent<HTMLAnchorElement>, hash: string) => {
    e.preventDefault();
    window.location.hash = hash;
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 aurora-background text-text-light dark:text-text-dark transition-colors duration-300">
      <div className="grid-overlay"></div>
       <div className="absolute top-4 right-4 z-20">
          <ThemeToggleButton />
       </div>
      <div className="relative z-10 w-full max-w-md">
        <div className="text-center mb-8">
            <a href="#/" onClick={(e) => navigateTo(e, '#/')} className="inline-block group">
                <LogoIcon className="h-12 w-12 text-accent-cyan group-hover:text-primary dark:group-hover:text-white transition-colors duration-300 [filter:drop-shadow(0_0_10px_#22D3EE)]"/>
            </a>
            <h1 className="text-3xl font-bold text-text-light dark:text-white mt-4">
                {getTitle()}
            </h1>
             <p className="text-sm text-text-secondary-light dark:text-text-secondary-dark mt-1">
                to continue to eComQnA
            </p>
        </div>
        
        <div className="relative bg-surface-light/60 dark:bg-surface-dark/50 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-border-glass-light dark:border-border-glass-dark">
          <div className="absolute -inset-px bg-gradient-to-r from-accent-pink to-accent-cyan rounded-2xl opacity-20 dark:opacity-30 blur-lg"></div>
          <div className="relative">
            <form onSubmit={handleAuthAction} className="space-y-6">
              {error && <div className="p-3 bg-red-500/10 dark:bg-red-900/50 border border-red-500/20 dark:border-red-500/50 text-red-700 dark:text-red-300 rounded-lg text-sm">{error}</div>}
              {message && <div className="p-3 bg-green-500/10 dark:bg-green-900/50 border border-green-500/20 dark:border-green-500/50 text-green-700 dark:text-green-300 rounded-lg text-sm">{message}</div>}
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Email address</label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1 w-full p-3 border border-border-glass-light dark:border-border-glass-dark rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 text-sm bg-black/5 dark:bg-black/20 text-text-light dark:text-text-dark placeholder:text-text-secondary-light dark:placeholder:text-text-secondary-dark focus:ring-accent-cyan/80 focus:outline-none"
                  placeholder="<EMAIL>"
                />
              </div>

              {view !== 'forgot' && (
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Password</label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete={view === 'signup' ? 'new-password' : 'current-password'}
                    required
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      if (view === 'signup') {
                        checkPasswordStrength(e.target.value);
                      }
                    }}
                    className="mt-1 w-full p-3 border border-border-glass-light dark:border-border-glass-dark rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 text-sm bg-black/5 dark:bg-black/20 text-text-light dark:text-text-dark placeholder:text-text-secondary-light dark:placeholder:text-text-secondary-dark focus:ring-accent-cyan/80 focus:outline-none"
                    placeholder="••••••••"
                  />
                  {view === 'signup' && password.length > 0 && (
                    <div className="mt-2">
                      <div className="flex justify-between items-center mb-1">
                          <span className="text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark">Password strength</span>
                          <span className={`text-xs font-bold ${
                            passwordStrength.score <= 2 ? 'text-red-500' :
                            passwordStrength.score <= 4 ? 'text-yellow-500' : 'text-green-500'
                          }`}>
                              {passwordStrength.label}
                          </span>
                      </div>
                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-1.5">
                          <div 
                              className={`h-1.5 rounded-full transition-all duration-300 ${passwordStrength.color}`} 
                              style={{ width: `${passwordStrength.score * 20}%` }}>
                          </div>
                      </div>
                    </div>
                  )}
                  {view === 'signin' && (
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" checked={rememberMe} onChange={(e) => setRememberMe(e.target.checked)} className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary dark:bg-slate-700 dark:border-slate-600"/>
                        <label htmlFor="remember-me" className="ml-2 block text-sm text-text-secondary-light dark:text-text-secondary-dark">
                          Remember me
                        </label>
                      </div>
                      <a href="#/forgot-password" onClick={(e) => navigateTo(e, '#/forgot-password')} className="text-sm text-accent-cyan hover:underline">Forgot password?</a>
                    </div>
                  )}
                </div>
              )}

              {view === 'signup' && (
                <div>
                  <label htmlFor="confirm-password" className="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Confirm Password</label>
                  <input
                    id="confirm-password"
                    name="confirm-password"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="mt-1 w-full p-3 border border-border-glass-light dark:border-border-glass-dark rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 text-sm bg-black/5 dark:bg-black/20 text-text-light dark:text-text-dark placeholder:text-text-secondary-light dark:placeholder:text-text-secondary-dark focus:ring-accent-cyan/80 focus:outline-none"
                    placeholder="••••••••"
                  />
                </div>
              )}
              
              <button
                type="submit"
                disabled={loading}
                className="relative group w-full flex items-center justify-center px-4 py-3 font-semibold text-white rounded-lg transition-all duration-200 ease-in-out shadow-lg bg-gradient-to-r from-accent-pink to-accent-cyan hover:shadow-accent-cyan/30 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading && <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>}
                {getButtonText()}
              </button>
            </form>
          </div>
        </div>

        <div className="text-center mt-6 text-sm text-text-secondary-light dark:text-text-secondary-dark">
            {view === 'signin' && <p>No account yet? <a href="#/signup" onClick={(e) => navigateTo(e, '#/signup')} className="font-medium text-accent-cyan hover:underline">Create one</a></p>}
            {view === 'signup' && <p>Already have an account? <a href="#/signin" onClick={(e) => navigateTo(e, '#/signin')} className="font-medium text-accent-cyan hover:underline">Sign in</a></p>}
            {view === 'forgot' && <p>Remembered your key? <a href="#/signin" onClick={(e) => navigateTo(e, '#/signin')} className="font-medium text-accent-cyan hover:underline">Sign in</a></p>}
            <p className="mt-4">
                <a href="#/" onClick={(e) => navigateTo(e, '#/')} className="font-medium text-text-secondary-light dark:text-text-secondary-dark hover:text-accent-cyan hover:underline transition-colors">
                    &larr; Back to Home
                </a>
            </p>
        </div>
      </div>
    </div>
  );
};