import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { Project } from '../types';
import { ProjectCard } from './ProjectCard';
import { CreateProjectModal } from './CreateProjectModal';
import { LogoIcon, MoonIcon, SunIcon, SignOutIcon } from './Icons';
import { getProjectsForUser, createNewProject } from '../services/projectService';

export const ProjectDashboard: React.FC = () => {
  const { currentUser, appUser, signout, theme, toggleTheme } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Load user projects
  useEffect(() => {
    const loadProjects = async () => {
      if (!appUser) return;

      try {
        setLoading(true);
        setError(null);
        const userProjects = await getProjectsForUser(appUser.id);
        setProjects(userProjects);
      } catch (err) {
        console.error('Error loading projects:', err);
        setError('Failed to load projects');
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, [appUser]);

  const handleCreateProject = async (name: string, websiteUrl?: string) => {
    if (!appUser) throw new Error('User not authenticated');

    try {
      const projectId = await createNewProject(appUser.id, name, websiteUrl);

      // Reload projects to include the new one
      const userProjects = await getProjectsForUser(appUser.id);
      setProjects(userProjects);

      // Navigate to the new project (for now, just close modal)
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error('Error creating project:', error);
      throw error;
    }
  };

  const handleProjectClick = (projectId: string) => {
    // Navigate to project management page
    window.location.hash = `#/project/${projectId}`;
  };

  const handleSignOut = async () => {
    try {
      await signout();
    } catch (error) {
      console.error("Failed to sign out", error);
    }
  };

  if (loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-secondary-light dark:bg-slate-900">
        <div className="animate-spin rounded-full h-24 w-24 border-t-2 border-b-2 border-accent-cyan"></div>
      </div>
    );
  }

  return (
    <div className="h-screen w-screen bg-secondary-light dark:bg-slate-900 text-text-light dark:text-text-dark flex flex-col font-sans transition-colors duration-300">
      {/* Header */}
      <header className="flex-shrink-0 bg-panel-light dark:bg-slate-800 border-b border-border-light dark:border-slate-700 px-6 py-3 transition-colors duration-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <LogoIcon className="h-7 w-7 text-primary" />
            <div>
              <h1 className="text-lg font-semibold text-text-light dark:text-slate-200">
                eComQnA
              </h1>
              <p className="text-xs text-text-secondary-light dark:text-slate-400">Project Dashboard</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {currentUser?.email && (
              <span className="text-sm text-text-secondary-light dark:text-slate-400">
                {currentUser.email}
              </span>
            )}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg hover:bg-secondary-light dark:hover:bg-slate-700 transition-colors"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <SunIcon className="w-5 h-5 text-text-secondary-light dark:text-slate-400" />
              ) : (
                <MoonIcon className="w-5 h-5 text-text-secondary-light dark:text-slate-400" />
              )}
            </button>
            <button
              onClick={handleSignOut}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <SignOutIcon className="w-4 h-4" />
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-text-light dark:text-slate-200">
                Your Projects
              </h2>
              <p className="text-text-secondary-light dark:text-slate-400 mt-1">
                Manage your chatbot projects and knowledge bases
              </p>
            </div>
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors font-medium"
            >
              + Create New Project
            </button>
          </div>

          {/* Error State */}
          {error && (
            <div className="mb-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg">
              <p className="text-red-700 dark:text-red-400">{error}</p>
            </div>
          )}

          {/* Projects Grid */}
          {projects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onClick={handleProjectClick}
                />
              ))}
            </div>
          ) : (
            /* Empty State */
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-secondary-light dark:bg-slate-700 rounded-full flex items-center justify-center mb-4">
                <LogoIcon className="w-12 h-12 text-text-secondary-light dark:text-slate-400" />
              </div>
              <h3 className="text-xl font-semibold text-text-light dark:text-slate-200 mb-2">
                No projects yet
              </h3>
              <p className="text-text-secondary-light dark:text-slate-400 mb-6 max-w-md mx-auto">
                Create your first chatbot project to get started. You can add knowledge bases,
                customize the chat experience, and embed it on your website.
              </p>
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors font-medium"
              >
                Create Your First Project
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateProject}
      />
    </div>
  );
};
