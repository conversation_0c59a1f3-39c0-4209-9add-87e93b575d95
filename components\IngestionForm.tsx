
import React, { useState } from 'react';
import { ProcessIcon, WarningIcon, LinkIcon, DocumentTextIcon } from './Icons';
import { ChunkVisualizer } from './ChunkVisualizer';
import { VectorData } from '../types';

interface IngestionFormProps {
  url: string;
  setUrl: (url: string) => void;
  text: string;
  setText: (text: string) => void;
  inputType: 'url' | 'text';
  setInputType: (type: 'url' | 'text') => void;
  onProcess: () => void;
  isLoading: boolean;
  isProcessed: boolean;
  ingestionStatus: string;
  vectorStore: VectorData[];
}

export const IngestionForm: React.FC<IngestionFormProps> = ({
  url,
  setUrl,
  text,
  setText,
  inputType,
  setInputType,
  onProcess,
  isLoading,
  isProcessed,
  ingestionStatus,
  vectorStore,
}) => {
  const [error, setError] = useState('');

  const handleValidationAndProcess = () => {
    if (inputType === 'url') {
      const urlPattern = new RegExp('^(https?|ftp)://[^\\s/$.?#].[^\\s]*$', 'i');
      if (!urlPattern.test(url)) {
        setError('Please enter a valid URL format (e.g., https://example.com).');
        return;
      }
    } else { // inputType === 'text'
      if (!text.trim()) {
        setError('Please enter some text to process.');
        return;
      }
    }
    setError('');
    onProcess();
  };

  const isButtonDisabled = isLoading || isProcessed || (inputType === 'url' && !url.trim()) || (inputType === 'text' && !text.trim());

  return (
    <div className="bg-panel dark:bg-slate-800 rounded-xl shadow-lg p-6 h-full flex flex-col transition-colors duration-300">
      <div>
        <h2 className="text-xl font-bold text-text-primary dark:text-slate-200 mb-2">
          1. Create Knowledge Base
        </h2>
        <p className="text-sm text-text-secondary dark:text-slate-400 mb-4">
          Provide a website URL or paste text to create a knowledge base.
        </p>

        <div className="flex border-b border-border-light dark:border-slate-700 mb-4">
          <button 
            onClick={() => setInputType('url')} 
            disabled={isLoading || isProcessed}
            className={`px-4 py-2 text-sm font-medium transition-colors rounded-t-lg -mb-px ${inputType === 'url' ? 'border-b-2 border-primary text-primary bg-primary/5 dark:bg-primary/10' : 'text-text-secondary dark:text-slate-400 hover:text-text-primary dark:hover:text-slate-200'}`}
            aria-current={inputType === 'url' ? 'page' : undefined}
          >
            From URL
          </button>
          <button 
            onClick={() => setInputType('text')} 
            disabled={isLoading || isProcessed}
            className={`px-4 py-2 text-sm font-medium transition-colors rounded-t-lg -mb-px ${inputType === 'text' ? 'border-b-2 border-primary text-primary bg-primary/5 dark:bg-primary/10' : 'text-text-secondary dark:text-slate-400 hover:text-text-primary dark:hover:text-slate-200'}`}
            aria-current={inputType === 'text' ? 'page' : undefined}
          >
            From Text
          </button>
        </div>

        {inputType === 'url' && (
          <>
            <div className="bg-blue-50 dark:bg-blue-900/50 border border-blue-200 dark:border-blue-800 p-4 rounded-lg mb-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 mt-0.5">
                  <WarningIcon className="h-5 w-5 text-blue-500" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800 dark:text-blue-300">
                    This feature uses a third-party proxy to fetch live web content. It may not work for all websites.
                  </p>
                </div>
              </div>
            </div>

            <div className="relative">
              <LinkIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                value={url}
                onChange={(e) => {
                  setUrl(e.target.value);
                  if (error) setError('');
                }}
                placeholder="https://example.com/faq"
                className={`w-full p-3 pl-10 border rounded-lg focus:ring-2 focus:border-transparent transition-shadow duration-200 text-sm bg-secondary/50 dark:bg-slate-700/50 dark:text-slate-200 dark:placeholder:text-gray-500 ${
                  error ? 'border-red-500 ring-red-500' : 'border-border-light dark:border-slate-600 focus:ring-primary'
                }`}
                disabled={isLoading || isProcessed}
              />
            </div>
          </>
        )}

        {inputType === 'text' && (
           <div className="relative">
              <DocumentTextIcon className="absolute left-3 top-3.5 w-5 h-5 text-gray-400 dark:text-gray-500" />
               <textarea
                value={text}
                onChange={(e) => {
                    setText(e.target.value);
                    if (error) setError('');
                }}
                placeholder="Paste or type the text you want to use for the knowledge base..."
                className={`w-full p-3 pl-10 border rounded-lg focus:ring-2 focus:border-transparent transition-shadow duration-200 text-sm bg-secondary/50 dark:bg-slate-700/50 dark:text-slate-200 dark:placeholder:text-gray-500 resize-y ${
                  error ? 'border-red-500 ring-red-500' : 'border-border-light dark:border-slate-600 focus:ring-primary'
                }`}
                rows={8}
                disabled={isLoading || isProcessed}
               />
           </div>
        )}

        {error && <p className="text-red-600 text-xs mt-2">{error}</p>}

        <button
          onClick={handleValidationAndProcess}
          disabled={isButtonDisabled}
          className={`mt-4 w-full flex items-center justify-center px-4 py-3 font-semibold text-white rounded-lg transition-all duration-200 ease-in-out shadow-md
            ${isProcessed ? 'bg-green-500 cursor-not-allowed' : ''}
            ${isLoading ? 'bg-gray-500 cursor-wait' : ''}
            ${!isLoading && !isProcessed ? 'bg-primary hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary' : ''}
            disabled:opacity-70 disabled:cursor-not-allowed`}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="truncate">{ingestionStatus || 'Processing...'}</span>
            </>
          ) : isProcessed ? (
            'Knowledge Base Ready'
          ) : (
            <>
              <ProcessIcon className="w-5 h-5 mr-2" />
              Process & Embed
            </>
          )}
        </button>
      </div>

      {isProcessed && vectorStore.length > 0 && (
        <div className="mt-6 pt-6 border-t border-border-light dark:border-slate-700 flex-grow min-h-0">
           <ChunkVisualizer chunks={vectorStore} />
        </div>
      )}
    </div>
  );
};