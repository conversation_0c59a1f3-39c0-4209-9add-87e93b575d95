import React, { useState } from 'react';
import { IngestionForm } from './components/IngestionForm';
import { ChatWindow } from './components/ChatWindow';
import { MessageInput } from './components/MessageInput';
import { ChatMessage, MessageRole, VectorData } from './types';
import { embedChunks, generateAnswer, embedContent } from './services/geminiService';
import { findTopK } from './utils/vector';
import { chunkText } from './utils/textChunking';
import { LogoIcon, MoonIcon, SunIcon, SignOutIcon } from './components/Icons';
import { useAuth } from './hooks/useAuth';

/**
 * Parses HTML string to extract clean, readable text.
 * It removes script, style, and common non-content tags, then collapses whitespace.
 * @param html The HTML content as a string.
 * @returns The extracted and cleaned text.
 */
const parseHtmlToText = (html: string): string => {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    // Remove tags that typically don't contain main content
    doc.querySelectorAll('script, style, nav, header, footer, aside, form, button, [aria-hidden="true"]').forEach(el => el.remove());
    
    const textContent = doc.body.textContent || '';
    
    // Collapse multiple spaces and newlines into a single space for cleaner text
    return textContent.replace(/(\r\n|\n|\r)/gm, " ").replace(/\s\s+/g, ' ').trim();
  } catch (error) {
    console.error("Error parsing HTML:", error);
    return ''; // Return empty string if parsing fails
  }
};


const ChatApp: React.FC = () => {
  const { currentUser, signout, theme, toggleTheme } = useAuth();
  const [ingestionUrl, setIngestionUrl] = useState<string>('');
  const [ingestionText, setIngestionText] = useState<string>('');
  const [inputType, setInputType] = useState<'url' | 'text'>('url');
  const [vectorStore, setVectorStore] = useState<VectorData[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      role: MessageRole.MODEL,
      content: "Hello! I'm your smart e-commerce assistant. To get started, provide your store's FAQ page URL or paste product information on the left. I'll create a knowledge base to answer customer questions.",
      timestamp: Date.now(),
    },
  ]);
  const [isIngesting, setIsIngesting] = useState<boolean>(false);
  const [ingestionStatus, setIngestionStatus] = useState<string>('');
  const [isAnswering, setIsAnswering] = useState<boolean>(false);

  const handleSignOut = async () => {
    try {
      await signout();
      // The router in App.tsx will handle the redirect automatically
    } catch (error) {
      console.error("Failed to sign out", error);
      // Optionally, show an error message to the user
      addMessage(MessageRole.SYSTEM, 'Error: Could not sign out.');
    }
  };

  const addMessage = (role: MessageRole, content: string) => {
    setMessages((prev) => [...prev, { role, content, timestamp: Date.now() }]);
  };

  const handleIngest = async () => {
    setIsIngesting(true);
    setVectorStore([]); // Clear previous knowledge base

    try {
      let contentToProcess = '';

      if (inputType === 'url') {
        if (!ingestionUrl.trim()) {
            addMessage(MessageRole.SYSTEM, 'Error: URL cannot be empty.');
            setIsIngesting(false);
            return;
        }
        setIngestionStatus(`Fetching content from URL...`);
        // Use a CORS proxy to fetch content from the URL due to browser security restrictions.
        const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(ingestionUrl)}`;
        const response = await fetch(proxyUrl);

        if (!response.ok) {
          throw new Error(`Failed to fetch content. Status: ${response.status}. The website may be blocking bots or the URL is invalid.`);
        }

        const html = await response.text();

        setIngestionStatus('Parsing website content...');
        await new Promise(resolve => setTimeout(resolve, 200));
        contentToProcess = parseHtmlToText(html);
      } else { // inputType === 'text'
        if (!ingestionText.trim()) {
          addMessage(MessageRole.SYSTEM, 'Error: Text content cannot be empty.');
          setIsIngesting(false);
          return;
        }
        contentToProcess = ingestionText;
      }
      
      if (!contentToProcess.trim()) {
        throw new Error("Could not extract any meaningful text content from the provided source.");
      }

      setIngestionStatus('Chunking website content...');
      await new Promise(resolve => setTimeout(resolve, 200));
      const chunks = chunkText(contentToProcess, 1000, 200);

      setIngestionStatus(`Embedding ${chunks.length} text chunks...`);
      const vectors = await embedChunks(chunks);
      
      setVectorStore(vectors);
      addMessage(MessageRole.SYSTEM, `Knowledge base created with ${vectors.length} document chunks. You can now ask questions.`);
    } catch (error) {
      console.error(error);
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred during ingestion.";
      addMessage(MessageRole.SYSTEM, `Error: ${errorMessage}`);
    } finally {
      setIngestionStatus('');
      setIsIngesting(false);
    }
  };

  const handleSendMessage = async (userMessage: string) => {
    addMessage(MessageRole.USER, userMessage);
    setIsAnswering(true);

    try {
      const queryEmbedding = await embedContent(userMessage);
      const topKContexts = findTopK(queryEmbedding, vectorStore, 3);
      const context = topKContexts.map((item) => item.text).join('\n---\n');
      
      if (!context) {
        addMessage(MessageRole.MODEL, "I'm sorry, I couldn't find any relevant information in the knowledge base to answer your question.");
        setIsAnswering(false);
        return;
      }
      
      const answer = await generateAnswer(userMessage, context);

      addMessage(MessageRole.MODEL, answer);

    } catch (error) {
      console.error(error);
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred while getting an answer.";
      addMessage(MessageRole.SYSTEM, `Error: ${errorMessage}`);
    } finally {
      setIsAnswering(false);
    }
  };

  const isKnowledgeBaseReady = vectorStore.length > 0;

  return (
    <div className="h-screen w-screen bg-secondary-light dark:bg-slate-900 text-text-light dark:text-text-dark flex flex-col font-sans transition-colors duration-300">
      <header className="flex-shrink-0 bg-panel-light dark:bg-slate-800 border-b border-border-light dark:border-slate-700 px-6 py-3 transition-colors duration-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <LogoIcon className="h-7 w-7 text-primary" />
            <div>
              <h1 className="text-lg font-semibold text-text-light dark:text-slate-200">
                eComQnA
              </h1>
              <p className="text-xs text-text-secondary-light dark:text-slate-400">Powered by Gemini & RAG</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
             {currentUser?.email && (
                <span className="text-sm text-text-secondary-light dark:text-slate-400 hidden sm:block truncate max-w-48" title={currentUser.email}>
                    {currentUser.email}
                </span>
             )}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-full text-text-secondary-light hover:bg-secondary-light dark:text-slate-400 dark:hover:bg-slate-700 transition-colors"
              aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
            >
              {theme === 'light' ? <MoonIcon className="w-5 h-5" /> : <SunIcon className="w-5 h-5" />}
            </button>
             <button
                onClick={handleSignOut}
                className="p-2 rounded-full text-text-secondary-light hover:bg-secondary-light dark:text-slate-400 dark:hover:bg-slate-700 transition-colors"
                aria-label="Sign out"
            >
                <SignOutIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      <main className="flex-grow grid grid-cols-1 md:grid-cols-3 gap-6 p-6 overflow-hidden">
        {/* Ingestion Panel */}
        <div className="md:col-span-1 h-full overflow-y-auto">
          <IngestionForm
            url={ingestionUrl}
            setUrl={setIngestionUrl}
            text={ingestionText}
            setText={setIngestionText}
            inputType={inputType}
            setInputType={setInputType}
            onProcess={handleIngest}
            isLoading={isIngesting}
            isProcessed={isKnowledgeBaseReady}
            ingestionStatus={ingestionStatus}
            vectorStore={vectorStore}
          />
        </div>

        {/* Chat Panel */}
        <div className="md:col-span-2 bg-panel-light dark:bg-panel-dark rounded-xl shadow-lg h-full flex flex-col overflow-hidden">
          <ChatWindow messages={messages} isLoading={isAnswering} />
          <MessageInput onSendMessage={handleSendMessage} disabled={!isKnowledgeBaseReady || isAnswering} />
        </div>
      </main>
    </div>
  );
};

export default ChatApp;