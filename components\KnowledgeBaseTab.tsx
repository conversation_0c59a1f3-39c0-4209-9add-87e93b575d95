import React, { useState, useEffect } from 'react';
import { Project, ProjectStatus, SourceType, DataSource } from '../types';
import {
  processAndStoreContent,
  fetchUrlContent,
  processFile,
  getProjectKnowledgeBaseStats,
  clearProjectKnowledgeBase
} from '../services/projectService';
import { LinkIcon, DocumentTextIcon, ProcessIcon, WarningIcon } from './Icons';

interface KnowledgeBaseTabProps {
  project: Project;
  onProjectUpdate: (updatedProject: Project) => void;
}

export const KnowledgeBaseTab: React.FC<KnowledgeBaseTabProps> = ({ 
  project, 
  onProjectUpdate 
}) => {
  const [inputType, setInputType] = useState<'url' | 'text' | 'file'>('url');
  const [urlInput, setUrlInput] = useState('');
  const [textInput, setTextInput] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState(project.knowledgeBaseStats);

  // Load current stats
  useEffect(() => {
    const loadStats = async () => {
      try {
        const currentStats = await getProjectKnowledgeBaseStats(project.id);
        setStats(currentStats);
      } catch (err) {
        console.error('Error loading stats:', err);
      }
    };
    
    loadStats();
  }, [project.id]);

  const addDataSource = () => {
    if (inputType === 'url' && urlInput.trim()) {
      const newSource: DataSource = {
        id: Date.now().toString(),
        type: SourceType.URL,
        reference: urlInput.trim(),
        addedAt: new Date(),
      };
      setDataSources([...dataSources, newSource]);
      setUrlInput('');
    } else if (inputType === 'text' && textInput.trim()) {
      const newSource: DataSource = {
        id: Date.now().toString(),
        type: SourceType.TEXT,
        reference: textInput.trim().substring(0, 100) + (textInput.length > 100 ? '...' : ''),
        addedAt: new Date(),
      };
      setDataSources([...dataSources, newSource]);
      setTextInput('');
    } else if (inputType === 'file' && selectedFile) {
      const newSource: DataSource = {
        id: Date.now().toString(),
        type: SourceType.FILE,
        reference: selectedFile.name,
        addedAt: new Date(),
      };
      setDataSources([...dataSources, newSource]);
      setSelectedFile(null);
    }
  };

  const removeDataSource = (id: string) => {
    setDataSources(dataSources.filter(source => source.id !== id));
  };

  const handleBuildKnowledgeBase = async () => {
    if (dataSources.length === 0) {
      setError('Please add at least one data source');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProcessingStatus('Preparing to build knowledge base...');

    try {
      let allContent = '';

      for (const source of dataSources) {
        if (source.type === SourceType.URL) {
          setProcessingStatus(`Fetching content from ${source.reference}...`);
          const urlContent = await fetchUrlContent(source.reference);
          allContent += urlContent + '\n\n';
        } else if (source.type === SourceType.TEXT) {
          setProcessingStatus(`Processing text content...`);
          allContent += textInput + '\n\n';
        } else if (source.type === SourceType.FILE) {
          setProcessingStatus(`Processing file: ${source.reference}...`);
          if (selectedFile) {
            const fileContent = await processFile(selectedFile);
            allContent += fileContent + '\n\n';
          }
        }
      }

      if (!allContent.trim()) {
        throw new Error('No content could be extracted from the provided sources');
      }

      setProcessingStatus('Processing and storing content...');

      // Use the first source as the primary reference
      const primarySource = dataSources[0];
      await processAndStoreContent(
        project.id,
        allContent,
        primarySource.type,
        primarySource.reference
      );

      // Reload stats
      const updatedStats = await getProjectKnowledgeBaseStats(project.id);
      setStats(updatedStats);

      // Update project status
      const updatedProject = {
        ...project,
        status: ProjectStatus.READY,
        knowledgeBaseStats: {
          chunkCount: updatedStats.chunkCount,
          lastTrainingDate: new Date(),
        },
      };
      onProjectUpdate(updatedProject);

      // Clear data sources after successful build
      setDataSources([]);
      setTextInput('');
      setSelectedFile(null);
      setProcessingStatus('');

    } catch (err) {
      console.error('Error building knowledge base:', err);
      setError(err instanceof Error ? err.message : 'Failed to build knowledge base');
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
    }
  };

  const handleClearKnowledgeBase = async () => {
    if (!confirm('Are you sure you want to clear the entire knowledge base? This action cannot be undone.')) {
      return;
    }

    try {
      await clearProjectKnowledgeBase(project.id);
      
      const updatedStats = await getProjectKnowledgeBaseStats(project.id);
      setStats(updatedStats);
      
      const updatedProject = {
        ...project,
        status: ProjectStatus.INACTIVE,
        knowledgeBaseStats: {
          chunkCount: 0,
        },
      };
      onProjectUpdate(updatedProject);
      
    } catch (err) {
      console.error('Error clearing knowledge base:', err);
      setError(err instanceof Error ? err.message : 'Failed to clear knowledge base');
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Data Sources Section */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Data Sources
        </h3>
        
        {/* Input Type Toggle */}
        <div className="flex bg-secondary-light dark:bg-slate-700 rounded-lg p-1 mb-4 w-fit">
          <button
            onClick={() => setInputType('url')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              inputType === 'url'
                ? 'bg-primary text-white'
                : 'text-text-secondary-light dark:text-slate-400 hover:text-text-light dark:hover:text-slate-200'
            }`}
          >
            <LinkIcon className="w-4 h-4 inline mr-2" />
            URL
          </button>
          <button
            onClick={() => setInputType('text')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              inputType === 'text'
                ? 'bg-primary text-white'
                : 'text-text-secondary-light dark:text-slate-400 hover:text-text-light dark:hover:text-slate-200'
            }`}
          >
            <DocumentTextIcon className="w-4 h-4 inline mr-2" />
            Text
          </button>
          <button
            onClick={() => setInputType('file')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              inputType === 'file'
                ? 'bg-primary text-white'
                : 'text-text-secondary-light dark:text-slate-400 hover:text-text-light dark:hover:text-slate-200'
            }`}
          >
            <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            File
          </button>
        </div>

        {/* URL Input */}
        {inputType === 'url' && (
          <div className="flex gap-3 mb-4">
            <input
              type="url"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              placeholder="https://example.com/faq"
              className="flex-1 px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
              disabled={isProcessing}
            />
            <button
              onClick={addDataSource}
              disabled={!urlInput.trim() || isProcessing}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add URL
            </button>
          </div>
        )}

        {/* Text Input */}
        {inputType === 'text' && (
          <div className="mb-4">
            <textarea
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder="Paste your content here..."
              rows={6}
              className="w-full px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
              disabled={isProcessing}
            />
            <div className="flex justify-between items-center mt-2">
              <span className="text-sm text-text-secondary-light dark:text-slate-400">
                {textInput.length} characters
              </span>
              <button
                onClick={addDataSource}
                disabled={!textInput.trim() || isProcessing}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Text
              </button>
            </div>
          </div>
        )}

        {/* File Input */}
        {inputType === 'file' && (
          <div className="mb-4">
            <div className="border-2 border-dashed border-border-light dark:border-slate-600 rounded-lg p-6 text-center">
              <input
                type="file"
                accept=".txt,.md"
                onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                className="hidden"
                id="file-upload"
                disabled={isProcessing}
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center gap-2"
              >
                <svg className="w-8 h-8 text-text-secondary-light dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span className="text-text-light dark:text-slate-200">
                  {selectedFile ? selectedFile.name : 'Click to upload file'}
                </span>
                <span className="text-sm text-text-secondary-light dark:text-slate-400">
                  Supports .txt and .md files
                </span>
              </label>
            </div>
            {selectedFile && (
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm text-text-secondary-light dark:text-slate-400">
                  {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                </span>
                <button
                  onClick={addDataSource}
                  disabled={!selectedFile || isProcessing}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Add File
                </button>
              </div>
            )}
          </div>
        )}

        {/* Data Sources List */}
        {dataSources.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-text-light dark:text-slate-200">Added Sources:</h4>
            {dataSources.map((source) => (
              <div
                key={source.id}
                className="flex items-center justify-between p-3 bg-secondary-light dark:bg-slate-700 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {source.type === SourceType.URL ? (
                    <LinkIcon className="w-4 h-4 text-primary" />
                  ) : source.type === SourceType.FILE ? (
                    <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  ) : (
                    <DocumentTextIcon className="w-4 h-4 text-primary" />
                  )}
                  <span className="text-sm text-text-light dark:text-slate-200">
                    {source.reference}
                  </span>
                </div>
                <button
                  onClick={() => removeDataSource(source.id)}
                  disabled={isProcessing}
                  className="text-red-500 hover:text-red-700 transition-colors disabled:opacity-50"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Training Controls */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Training Controls
        </h3>
        
        {error && (
          <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg flex items-start gap-2">
            <WarningIcon className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <p className="text-red-700 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {isProcessing && (
          <div className="mb-4 p-3 bg-blue-100 dark:bg-blue-900/20 border border-blue-300 dark:border-blue-700 rounded-lg">
            <div className="flex items-center gap-2">
              <svg className="animate-spin h-4 w-4 text-blue-500" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              <span className="text-blue-700 dark:text-blue-400 text-sm">
                {processingStatus || 'Processing...'}
              </span>
            </div>
          </div>
        )}

        <div className="flex gap-3">
          <button
            onClick={handleBuildKnowledgeBase}
            disabled={dataSources.length === 0 || isProcessing}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ProcessIcon className="w-4 h-4" />
            {isProcessing ? 'Building...' : 'Build Knowledge Base'}
          </button>
          
          {stats.chunkCount > 0 && (
            <button
              onClick={handleClearKnowledgeBase}
              disabled={isProcessing}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Knowledge Base
            </button>
          )}
        </div>
      </div>

      {/* Knowledge Base Overview */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Knowledge Base Overview
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-secondary-light dark:bg-slate-700 rounded-lg">
            <div className="text-2xl font-bold text-primary dark:text-indigo-400">
              {stats.chunkCount}
            </div>
            <div className="text-sm text-text-secondary-light dark:text-slate-400">
              Knowledge Chunks
            </div>
          </div>
          
          <div className="text-center p-4 bg-secondary-light dark:bg-slate-700 rounded-lg">
            <div className="text-2xl font-bold text-primary dark:text-indigo-400">
              {stats.status === ProjectStatus.READY ? '✓' : '—'}
            </div>
            <div className="text-sm text-text-secondary-light dark:text-slate-400">
              Ready for Chat
            </div>
          </div>
          
          <div className="text-center p-4 bg-secondary-light dark:bg-slate-700 rounded-lg">
            <div className="text-2xl font-bold text-primary dark:text-indigo-400">
              {stats.lastTrainingDate 
                ? new Intl.DateTimeFormat('en-US', { 
                    month: 'short', 
                    day: 'numeric' 
                  }).format(stats.lastTrainingDate)
                : '—'
              }
            </div>
            <div className="text-sm text-text-secondary-light dark:text-slate-400">
              Last Training
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
