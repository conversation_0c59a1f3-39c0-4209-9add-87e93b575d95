import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { VectorData } from "../types";

// Ensure the API key is available, otherwise throw an error.
if (!process.env.API_KEY) {
  throw new Error("API_KEY environment variable not set.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

/**
 * Generates an embedding for a single piece of text.
 * @param text The text to embed.
 * @returns A promise that resolves to the embedding vector (an array of numbers).
 */
export const embedContent = async (text: string): Promise<number[]> => {
  try {
    // FIX: The parameter name is 'contents', not 'content' (Error on line 21). We pass `text` in an array for consistency.
    const result = await ai.models.embedContent({
      model: "text-embedding-004",
      contents: [text],
    });
    // FIX: The response property is 'embeddings' (plural), not 'embedding' (Error on line 23).
    if (!result.embeddings?.[0]?.values) {
      throw new Error("Invalid embedding response from API.");
    }
    // FIX: The response property is 'embeddings' (plural). Access the first element for a single request (Error on line 26).
    return result.embeddings[0].values;
  } catch (error) {
    console.error("Error generating embedding:", error);
    throw new Error("Failed to generate embedding.");
  }
};


/**
 * Processes an array of text chunks to generate embeddings for each using a single batch request for efficiency.
 * @param chunks An array of text strings.
 * @returns A promise that resolves to an array of VectorData objects.
 */
export const embedChunks = async (chunks: string[]): Promise<VectorData[]> => {
  try {
    // FIX: The `batchEmbedContent` method does not exist. Use `embedContent` for batch operations (Error on line 42).
    const result = await ai.models.embedContent({
      model: "text-embedding-004",
      // FIX: For batching, pass the array of strings directly to the `contents` property.
      contents: chunks,
    });

    return result.embeddings.map((embedding, index) => ({
      text: chunks[index],
      embedding: embedding.values,
    }));
  } catch(error) {
    console.error("Error batch-embedding chunks:", error);
    throw new Error("Failed to process chunks for embedding.");
  }
};


/**
 * Generates a context-aware answer using Gemini 2.5 Flash.
 * @param query The user's original question.
 * @param context A string containing the relevant context retrieved from the vector store.
 * @returns A promise that resolves to the model's generated answer as a string.
 */
export const generateAnswer = async (query: string, context: string): Promise<string> => {
  const systemInstruction = `You are an expert customer support assistant. 
Your goal is to provide helpful and accurate answers based *only* on the context provided.
If the context does not contain the answer to the question, state that you don't have enough information to answer.
Do not make up information or answer based on prior knowledge.
Provide a comprehensive and detailed answer. Explain your reasoning and quote relevant parts from the context when possible. Use a clear, easy-to-read format like bullet points or numbered lists if it helps explain things better.`;

  const prompt = `
CONTEXT:
---
${context}
---

QUESTION:
${query}
`;

  try {
     const response: GenerateContentResponse = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: {
            systemInstruction: systemInstruction,
        }
     });
     return response.text;
  } catch (error) {
    console.error("Error generating answer:", error);
    throw new Error("Failed to generate an answer from the model.");
  }
};