
import { VectorData } from '../types';

/**
 * Calculates the dot product of two vectors.
 * @param vecA - The first vector.
 * @param vecB - The second vector.
 * @returns The dot product.
 */
function dotProduct(vecA: number[], vecB: number[]): number {
  let product = 0;
  for (let i = 0; i < vecA.length; i++) {
    product += vecA[i] * vecB[i];
  }
  return product;
}

/**
 * Calculates the magnitude (or L2 norm) of a vector.
 * @param vec - The vector.
 * @returns The magnitude of the vector.
 */
function magnitude(vec: number[]): number {
  let sum = 0;
  for (let i = 0; i < vec.length; i++) {
    sum += vec[i] * vec[i];
  }
  return Math.sqrt(sum);
}

/**
 * Calculates the cosine similarity between two vectors.
 * @param vecA - The first vector.
 * @param vecB - The second vector.
 * @returns A similarity score between -1 and 1.
 */
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  const dot = dotProduct(vecA, vecB);
  const magA = magnitude(vecA);
  const magB = magnitude(vecB);
  if (magA === 0 || magB === 0) {
    return 0; // Avoid division by zero
  }
  return dot / (magA * magB);
}

/**
 * Finds the top-k most similar vectors from a vector store based on cosine similarity.
 * @param queryEmbedding - The embedding of the user's query.
 * @param vectorStore - The array of stored vectors with their text.
 * @param k - The number of top results to return.
 * @returns An array of the top-k most similar VectorData objects.
 */
export function findTopK(
  queryEmbedding: number[],
  vectorStore: VectorData[],
  k: number
): VectorData[] {
  if (k <= 0) {
    return [];
  }

  const similarities: { item: VectorData; score: number }[] = vectorStore.map(
    (item) => ({
      item,
      score: cosineSimilarity(queryEmbedding, item.embedding),
    })
  );

  similarities.sort((a, b) => b.score - a.score);

  return similarities.slice(0, k).map((sim) => sim.item);
}
