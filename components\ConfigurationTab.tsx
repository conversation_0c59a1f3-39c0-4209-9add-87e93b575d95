import React, { useState, useEffect } from 'react';
import { Project, ProjectConfig } from '../types';
import { updateProjectConfig } from '../services/projectService';

interface ConfigurationTabProps {
  project: Project;
  onProjectUpdate: (updatedProject: Project) => void;
}

export const ConfigurationTab: React.FC<ConfigurationTabProps> = ({ 
  project, 
  onProjectUpdate 
}) => {
  const [config, setConfig] = useState<ProjectConfig>(project.config);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Track changes
  useEffect(() => {
    const hasConfigChanges = JSON.stringify(config) !== JSON.stringify(project.config);
    setHasChanges(hasConfigChanges);
  }, [config, project.config]);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);

    try {
      await updateProjectConfig(project.id, config);
      
      const updatedProject = {
        ...project,
        config,
      };
      onProjectUpdate(updatedProject);
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving configuration:', error);
      setSaveError(error instanceof Error ? error.message : 'Failed to save configuration');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setConfig(project.config);
    setHasChanges(false);
    setSaveError(null);
  };

  const updateConfig = (updates: Partial<ProjectConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const addSuggestedQuestion = () => {
    if (config.suggestedQuestions.length < 4) {
      updateConfig({
        suggestedQuestions: [...config.suggestedQuestions, '']
      });
    }
  };

  const updateSuggestedQuestion = (index: number, value: string) => {
    const newQuestions = [...config.suggestedQuestions];
    newQuestions[index] = value;
    updateConfig({ suggestedQuestions: newQuestions });
  };

  const removeSuggestedQuestion = (index: number) => {
    const newQuestions = config.suggestedQuestions.filter((_, i) => i !== index);
    updateConfig({ suggestedQuestions: newQuestions });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Save Controls */}
      {hasChanges && (
        <div className="bg-blue-100 dark:bg-blue-900/20 border border-blue-300 dark:border-blue-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-blue-700 dark:text-blue-400 text-sm">
              You have unsaved changes
            </p>
            <div className="flex gap-2">
              <button
                onClick={handleReset}
                disabled={isSaving}
                className="px-3 py-1 text-sm text-blue-700 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800 rounded transition-colors disabled:opacity-50"
              >
                Reset
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-1"
              >
                {isSaving && (
                  <svg className="animate-spin h-3 w-3" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                )}
                {isSaving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Save Error */}
      {saveError && (
        <div className="bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-400 text-sm">{saveError}</p>
        </div>
      )}

      {/* Appearance Settings */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-6">
          Appearance Settings
        </h3>
        
        <div className="space-y-6">
          {/* Theme Color */}
          <div>
            <label className="block text-sm font-medium text-text-light dark:text-slate-200 mb-2">
              Theme Color
            </label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                value={config.themeColor}
                onChange={(e) => updateConfig({ themeColor: e.target.value })}
                className="w-12 h-10 rounded border border-border-light dark:border-slate-600 cursor-pointer"
              />
              <input
                type="text"
                value={config.themeColor}
                onChange={(e) => updateConfig({ themeColor: e.target.value })}
                placeholder="#4f46e5"
                className="px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
              />
            </div>
            <p className="text-xs text-text-secondary-light dark:text-slate-400 mt-1">
              This color will be used for the chat widget and buttons
            </p>
          </div>

          {/* Welcome Message */}
          <div>
            <label className="block text-sm font-medium text-text-light dark:text-slate-200 mb-2">
              Welcome Message
            </label>
            <textarea
              value={config.welcomeMessage}
              onChange={(e) => updateConfig({ welcomeMessage: e.target.value })}
              placeholder="Hello! How can I help you today?"
              rows={3}
              maxLength={500}
              className="w-full px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
            />
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-text-secondary-light dark:text-slate-400">
                First message users see when opening the chat
              </p>
              <span className="text-xs text-text-secondary-light dark:text-slate-400">
                {config.welcomeMessage.length}/500
              </span>
            </div>
          </div>

          {/* Header Logo */}
          <div>
            <label className="block text-sm font-medium text-text-light dark:text-slate-200 mb-2">
              Header Logo URL (Optional)
            </label>
            <input
              type="url"
              value={config.headerLogo || ''}
              onChange={(e) => updateConfig({ headerLogo: e.target.value || undefined })}
              placeholder="https://example.com/logo.png"
              className="w-full px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
            />
            <p className="text-xs text-text-secondary-light dark:text-slate-400 mt-1">
              Logo displayed in the chat widget header
            </p>
          </div>
        </div>
      </div>

      {/* Behavior Settings */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-6">
          Behavior Settings
        </h3>
        
        <div className="space-y-6">
          {/* System Prompt */}
          <div>
            <label className="block text-sm font-medium text-text-light dark:text-slate-200 mb-2">
              System Prompt
            </label>
            <textarea
              value={config.systemPrompt}
              onChange={(e) => updateConfig({ systemPrompt: e.target.value })}
              placeholder="You are a helpful customer support assistant..."
              rows={6}
              className="w-full px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200 font-mono text-sm"
            />
            <p className="text-xs text-text-secondary-light dark:text-slate-400 mt-1">
              Instructions that guide how the AI responds to users
            </p>
          </div>

          {/* Suggested Questions */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-text-light dark:text-slate-200">
                Suggested Questions
              </label>
              <button
                onClick={addSuggestedQuestion}
                disabled={config.suggestedQuestions.length >= 4}
                className="px-3 py-1 text-sm bg-primary text-white rounded hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Question
              </button>
            </div>
            
            <div className="space-y-3">
              {config.suggestedQuestions.map((question, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={question}
                    onChange={(e) => updateSuggestedQuestion(index, e.target.value)}
                    placeholder={`Suggested question ${index + 1}`}
                    maxLength={100}
                    className="flex-1 px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
                  />
                  <button
                    onClick={() => removeSuggestedQuestion(index)}
                    className="px-3 py-2 text-red-500 hover:text-red-700 transition-colors"
                    aria-label="Remove question"
                  >
                    ×
                  </button>
                </div>
              ))}
              
              {config.suggestedQuestions.length === 0 && (
                <p className="text-text-secondary-light dark:text-slate-400 text-sm italic">
                  No suggested questions added yet
                </p>
              )}
            </div>
            
            <p className="text-xs text-text-secondary-light dark:text-slate-400 mt-2">
              Quick questions users can click to start conversations (max 4)
            </p>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Preview
        </h3>
        
        <div className="bg-secondary-light dark:bg-slate-700 rounded-lg p-4">
          <div 
            className="rounded-lg p-4 text-white"
            style={{ backgroundColor: config.themeColor }}
          >
            <div className="flex items-center gap-2 mb-3">
              {config.headerLogo && (
                <img 
                  src={config.headerLogo} 
                  alt="Logo" 
                  className="w-6 h-6 rounded"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              )}
              <span className="font-medium">Chat Widget</span>
            </div>
            
            <div className="bg-white bg-opacity-20 rounded p-3 mb-3">
              <p className="text-sm">{config.welcomeMessage}</p>
            </div>
            
            {config.suggestedQuestions.length > 0 && (
              <div className="space-y-2">
                {config.suggestedQuestions.filter(q => q.trim()).map((question, index) => (
                  <button
                    key={index}
                    className="block w-full text-left px-3 py-2 bg-white bg-opacity-20 rounded text-sm hover:bg-opacity-30 transition-colors"
                  >
                    {question}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
