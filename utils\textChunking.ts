/**
 * Implements a more sophisticated chunking strategy using a sliding window.
 * This approach creates overlapping chunks to ensure that semantic context is not lost
 * at the boundaries of chunks, which is crucial for effective RAG.
 *
 * @param text The raw text content to chunk.
 * @param chunkSize The target maximum size for each chunk in characters.
 * @param overlapSize The number of characters from the end of one chunk to include at the beginning of the next.
 * @returns An array of text chunks.
 */
export const chunkText = (
  text: string,
  chunkSize = 1000,
  overlapSize = 200
): string[] => {
  if (overlapSize >= chunkSize) {
    throw new Error("Overlap size must be smaller than chunk size.");
  }

  // 1. Pre-process text: replace newlines and collapse multiple spaces.
  const processedText = text.replace(/(\r\n|\n|\r)/gm, " ").replace(/\s+/g, ' ').trim();

  if (processedText.length <= chunkSize) {
    return [processedText];
  }

  const chunks: string[] = [];
  let startIndex = 0;

  // 2. Iterate through the text with a sliding window.
  while (startIndex < processedText.length) {
    // 3. Determine the end of the current chunk.
    let endIndex = startIndex + chunkSize;

    // If the endIndex goes past the end of the text, clamp it to the end.
    if (endIndex > processedText.length) {
      endIndex = processedText.length;
    } else {
      // 4. Find the last space before the endIndex to avoid splitting a word.
      // We search backwards from the intended endIndex.
      const lastSpaceIndex = processedText.lastIndexOf(' ', endIndex);
      if (lastSpaceIndex > startIndex) {
        endIndex = lastSpaceIndex;
      }
    }
    
    const chunk = processedText.substring(startIndex, endIndex).trim();
    if (chunk) {
      chunks.push(chunk);
    }

    // 5. Move the start index for the next chunk, creating an overlap.
    const nextStartIndex = startIndex + chunkSize - overlapSize;

    // 6. If the next start index is the same as the current one (e.g., due to a very short
    // chunk at the end), we must advance it to prevent an infinite loop.
    if (nextStartIndex <= startIndex) {
        startIndex = endIndex;
    } else {
        startIndex = nextStartIndex;
    }
  }

  return chunks;
};