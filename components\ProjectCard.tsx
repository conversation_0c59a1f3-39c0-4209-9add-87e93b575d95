import React from 'react';
import { Project, ProjectStatus } from '../types';

interface ProjectCardProps {
  project: Project;
  onClick: (projectId: string) => void;
}

const getStatusBadge = (status: ProjectStatus) => {
  switch (status) {
    case ProjectStatus.READY:
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          Ready
        </span>
      );
    case ProjectStatus.TRAINING:
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
          Training
        </span>
      );
    case ProjectStatus.INACTIVE:
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
          Inactive
        </span>
      );
    default:
      return null;
  }
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  }).format(date);
};

export const ProjectCard: React.FC<ProjectCardProps> = ({ project, onClick }) => {
  const handleClick = () => {
    onClick(project.id);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick(project.id);
    }
  };

  return (
    <div
      className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg border border-border-light dark:border-slate-700 p-6 cursor-pointer transition-all duration-200 hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-slate-900"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Open project ${project.name}`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 truncate">
            {project.name}
          </h3>
          {project.websiteUrl && (
            <p className="text-sm text-text-secondary-light dark:text-slate-400 truncate mt-1">
              {project.websiteUrl}
            </p>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          {getStatusBadge(project.status)}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-secondary-light dark:bg-slate-700/50 rounded-lg">
          <div className="text-2xl font-bold text-primary dark:text-indigo-400">
            {project.knowledgeBaseStats.chunkCount}
          </div>
          <div className="text-xs text-text-secondary-light dark:text-slate-400">
            Knowledge Chunks
          </div>
        </div>
        <div className="text-center p-3 bg-secondary-light dark:bg-slate-700/50 rounded-lg">
          <div className="text-2xl font-bold text-primary dark:text-indigo-400">
            {project.status === ProjectStatus.READY ? '✓' : '—'}
          </div>
          <div className="text-xs text-text-secondary-light dark:text-slate-400">
            Chat Ready
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between text-xs text-text-secondary-light dark:text-slate-400">
        <span>
          Created {formatDate(project.createdAt)}
        </span>
        {project.knowledgeBaseStats.lastTrainingDate && (
          <span>
            Trained {formatDate(project.knowledgeBaseStats.lastTrainingDate)}
          </span>
        )}
      </div>

      {/* Hover indicator */}
      <div className="mt-4 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <span className="text-sm text-primary dark:text-indigo-400 font-medium">
          Click to manage →
        </span>
      </div>
    </div>
  );
};
