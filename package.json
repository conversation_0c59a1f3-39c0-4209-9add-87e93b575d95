{"name": "ecomqna", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1", "@google/genai": "^1.17.0", "marked": "^16.2.1", "firebase": "^12.2.1"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.8.2", "vite": "^6.2.0", "vitest": "^2.1.8", "@vitest/ui": "^2.1.8", "jsdom": "^26.0.0"}}