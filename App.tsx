import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from './hooks/useAuth';
import ChatApp from './ChatApp';
import { LandingPage } from './pages/LandingPage';
import { AuthPage } from './pages/AuthPage';
import { ProjectDashboard } from './components/ProjectDashboard';
import { ProjectManagement } from './components/ProjectManagement';
import { PublicChatInterface } from './components/PublicChatInterface';

// Simple Hash Router Hook
const useHash = () => {
    const [hash, setHash] = useState(window.location.hash);

    const onHashChange = useCallback(() => {
        setHash(window.location.hash);
    }, []);

    useEffect(() => {
        window.addEventListener('hashchange', onHashChange);
        return () => {
            window.removeEventListener('hashchange', onHashChange);
        };
    }, [onHashChange]);

    return hash;
};

const App: React.FC = () => {
    const { currentUser, loading } = useAuth();
    const hash = useHash();

    useEffect(() => {
        // If user is logged in, ensure they are at the app root or project pages, not auth pages
        if (currentUser && (hash === '#/signin' || hash === '#/signup' || hash === '#/forgot-password')) {
            window.location.hash = '#/';
        }
    }, [currentUser, hash]);

    if (loading) {
        return (
            <div className="h-screen w-screen flex items-center justify-center bg-background-light dark:bg-background-dark">
                <div className="animate-spin rounded-full h-24 w-24 border-t-2 border-b-2 border-accent-cyan"></div>
            </div>
        );
    }

    // Handle public chat interface (no authentication required)
    if (hash.startsWith('#/chat/')) {
        const projectId = hash.split('/')[2];
        if (projectId) {
            return <PublicChatInterface projectId={projectId} />;
        }
    }

    if (currentUser) {
        // Handle project-specific routing
        if (hash.startsWith('#/project/')) {
            const projectId = hash.split('/')[2];
            if (projectId) {
                return <ProjectManagement projectId={projectId} />;
            }
        }

        // Handle legacy chat route for backward compatibility
        if (hash === '#/chat') {
            return <ChatApp />;
        }

        // Default authenticated route: Project Dashboard
        return <ProjectDashboard />;
    }

    switch (hash) {
        case '#/signin':
        case '#/signup':
        case '#/forgot-password':
            return <AuthPage initialRoute={hash} />;
        default:
            return <LandingPage />;
    }
};

export default App;