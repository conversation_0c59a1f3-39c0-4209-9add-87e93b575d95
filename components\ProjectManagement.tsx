import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { Project } from '../types';
import { getProjectById } from '../services/projectService';
import { LogoIcon, MoonIcon, SunIcon, SignOutIcon } from './Icons';
import { KnowledgeBaseTab } from './KnowledgeBaseTab';
import { ConfigurationTab } from './ConfigurationTab';
import { IntegrationTab } from './IntegrationTab';

interface ProjectManagementProps {
  projectId: string;
}

type TabType = 'knowledge' | 'config' | 'integration';

export const ProjectManagement: React.FC<ProjectManagementProps> = ({ projectId }) => {
  const { currentUser, signout, theme, toggleTheme } = useAuth();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>('knowledge');

  // Load project data
  useEffect(() => {
    const loadProject = async () => {
      try {
        setLoading(true);
        setError(null);
        const projectData = await getProjectById(projectId);
        
        if (!projectData) {
          setError('Project not found');
          return;
        }
        
        setProject(projectData);
      } catch (err) {
        console.error('Error loading project:', err);
        setError('Failed to load project');
      } finally {
        setLoading(false);
      }
    };

    loadProject();
  }, [projectId]);

  const handleSignOut = async () => {
    try {
      await signout();
    } catch (error) {
      console.error("Failed to sign out", error);
    }
  };

  const handleBackToDashboard = () => {
    window.location.hash = '#/';
  };

  const handleProjectUpdate = (updatedProject: Project) => {
    setProject(updatedProject);
  };

  if (loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-secondary-light dark:bg-slate-900">
        <div className="animate-spin rounded-full h-24 w-24 border-t-2 border-b-2 border-accent-cyan"></div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="h-screen w-screen bg-secondary-light dark:bg-slate-900 text-text-light dark:text-text-dark flex flex-col font-sans">
        <header className="flex-shrink-0 bg-panel-light dark:bg-slate-800 border-b border-border-light dark:border-slate-700 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <LogoIcon className="h-7 w-7 text-primary" />
              <h1 className="text-lg font-semibold text-text-light dark:text-slate-200">
                eComQnA
              </h1>
            </div>
            <button
              onClick={handleSignOut}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <SignOutIcon className="w-4 h-4" />
              Sign Out
            </button>
          </div>
        </header>
        
        <main className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-text-light dark:text-slate-200">
              {error || 'Project Not Found'}
            </h2>
            <button
              onClick={handleBackToDashboard}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </main>
      </div>
    );
  }

  const tabs = [
    { id: 'knowledge' as TabType, label: 'Knowledge Base', icon: '📚' },
    { id: 'config' as TabType, label: 'Configuration', icon: '⚙️' },
    { id: 'integration' as TabType, label: 'Integration', icon: '🔗' },
  ];

  return (
    <div className="h-screen w-screen bg-secondary-light dark:bg-slate-900 text-text-light dark:text-text-dark flex flex-col font-sans transition-colors duration-300">
      {/* Header */}
      <header className="flex-shrink-0 bg-panel-light dark:bg-slate-800 border-b border-border-light dark:border-slate-700 px-6 py-3 transition-colors duration-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button
              onClick={handleBackToDashboard}
              className="p-1 rounded hover:bg-secondary-light dark:hover:bg-slate-700 transition-colors"
              aria-label="Back to dashboard"
            >
              <svg className="w-5 h-5 text-text-secondary-light dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <LogoIcon className="h-7 w-7 text-primary" />
            <div>
              <h1 className="text-lg font-semibold text-text-light dark:text-slate-200">
                {project.name}
              </h1>
              <p className="text-xs text-text-secondary-light dark:text-slate-400">Project Management</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {currentUser?.email && (
              <span className="text-sm text-text-secondary-light dark:text-slate-400">
                {currentUser.email}
              </span>
            )}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg hover:bg-secondary-light dark:hover:bg-slate-700 transition-colors"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <SunIcon className="w-5 h-5 text-text-secondary-light dark:text-slate-400" />
              ) : (
                <MoonIcon className="w-5 h-5 text-text-secondary-light dark:text-slate-400" />
              )}
            </button>
            <button
              onClick={handleSignOut}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <SignOutIcon className="w-4 h-4" />
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {/* Project Info Bar */}
      <div className="bg-panel-light dark:bg-slate-800 border-b border-border-light dark:border-slate-700 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <div>
              <span className="text-sm text-text-secondary-light dark:text-slate-400">Status:</span>
              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                project.status === 'ready' 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : project.status === 'training'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}>
                {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
              </span>
            </div>
            <div>
              <span className="text-sm text-text-secondary-light dark:text-slate-400">Knowledge Chunks:</span>
              <span className="ml-2 font-medium text-text-light dark:text-slate-200">
                {project.knowledgeBaseStats.chunkCount}
              </span>
            </div>
            {project.websiteUrl && (
              <div>
                <span className="text-sm text-text-secondary-light dark:text-slate-400">Website:</span>
                <a 
                  href={project.websiteUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="ml-2 text-primary hover:text-primary-hover transition-colors"
                >
                  {project.websiteUrl}
                </a>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-panel-light dark:bg-slate-800 border-b border-border-light dark:border-slate-700">
        <div className="px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-text-secondary-light dark:text-slate-400 hover:text-text-light dark:hover:text-slate-200 hover:border-border-light dark:hover:border-slate-600'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <main className="flex-grow overflow-y-auto">
        <div className="p-6">
          {activeTab === 'knowledge' && (
            <KnowledgeBaseTab
              project={project}
              onProjectUpdate={handleProjectUpdate}
            />
          )}

          {activeTab === 'config' && (
            <ConfigurationTab
              project={project}
              onProjectUpdate={handleProjectUpdate}
            />
          )}

          {activeTab === 'integration' && (
            <IntegrationTab project={project} />
          )}
        </div>
      </main>
    </div>
  );
};
