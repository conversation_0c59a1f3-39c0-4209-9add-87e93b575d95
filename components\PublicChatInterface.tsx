import React, { useState, useEffect } from 'react';
import { ProjectChat } from './ProjectChat';

interface PublicChatInterfaceProps {
  projectId: string;
}

export const PublicChatInterface: React.FC<PublicChatInterfaceProps> = ({ projectId }) => {
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Check if we're in an iframe
  useEffect(() => {
    const inIframe = window.self !== window.top;
    setIsFullscreen(!inIframe);
  }, []);

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  if (error) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-secondary-light dark:bg-slate-900 p-6">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-2">
            Chat Unavailable
          </h3>
          <p className="text-text-secondary-light dark:text-slate-400 text-sm">
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`${isFullscreen ? 'h-screen w-screen' : 'h-full w-full'} bg-secondary-light dark:bg-slate-900`}>
      <ProjectChat 
        projectId={projectId} 
        embedded={!isFullscreen}
        onError={handleError}
      />
    </div>
  );
};
