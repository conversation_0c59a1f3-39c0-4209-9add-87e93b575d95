import { Project, ChatMessage, MessageRole, KnowledgeChunk } from '../types';
import { getProjectChunks } from './firestoreService';
import { embedContent, generateAnswer } from './geminiService';
import { findTopK } from '../utils/vector';

/**
 * Project-specific chat service that uses Firestore-stored knowledge base
 */

export interface ProjectChatContext {
  project: Project;
  knowledgeChunks: KnowledgeChunk[];
}

/**
 * Initialize chat context for a project by loading its knowledge base
 */
export const initializeProjectChat = async (projectId: string): Promise<ProjectChatContext | null> => {
  try {
    const chunks = await getProjectChunks(projectId);
    
    if (chunks.length === 0) {
      return null; // No knowledge base available
    }

    // Note: We'll need to get the project data separately if needed
    // For now, we'll focus on the knowledge chunks
    return {
      project: {} as Project, // Placeholder - would need to fetch project data
      knowledgeChunks: chunks,
    };
  } catch (error) {
    console.error('Error initializing project chat:', error);
    throw new Error('Failed to initialize chat context');
  }
};

/**
 * Generate a response for a user message using project-specific knowledge base
 */
export const generateProjectResponse = async (
  userMessage: string,
  context: ProjectChatContext,
  customSystemPrompt?: string
): Promise<string> => {
  try {
    // Generate embedding for the user's question
    const queryEmbedding = await embedContent(userMessage);
    
    // Convert knowledge chunks to vector format for similarity search
    const vectorData = context.knowledgeChunks.map(chunk => ({
      text: chunk.content,
      embedding: chunk.embedding,
    }));
    
    // Find the most relevant chunks (top 3)
    const topKContexts = findTopK(queryEmbedding, vectorData, 3);
    const relevantContext = topKContexts.map(item => item.text).join('\n---\n');
    
    if (!relevantContext.trim()) {
      return "I'm sorry, I couldn't find any relevant information in the knowledge base to answer your question.";
    }
    
    // Use custom system prompt if provided, otherwise use default
    const systemInstruction = customSystemPrompt || `You are a helpful customer support assistant. 
Your goal is to provide helpful and accurate answers based *only* on the context provided.
If the context does not contain the answer to the question, state that you don't have enough information to answer.
Do not make up information or answer based on prior knowledge.
Provide a comprehensive and detailed answer. Use a clear, easy-to-read format when helpful.`;

    // Generate answer using the relevant context
    const answer = await generateAnswerWithCustomPrompt(userMessage, relevantContext, systemInstruction);
    
    return answer;
  } catch (error) {
    console.error('Error generating project response:', error);
    throw new Error('Failed to generate response');
  }
};

/**
 * Enhanced version of generateAnswer that accepts custom system prompt
 */
const generateAnswerWithCustomPrompt = async (
  query: string, 
  context: string, 
  systemInstruction: string
): Promise<string> => {
  const prompt = `
CONTEXT:
---
${context}
---

QUESTION:
${query}
`;

  try {
    const { GoogleGenAI } = await import("@google/genai");
    
    if (!process.env.API_KEY) {
      throw new Error("API_KEY environment variable not set.");
    }
    
    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
    
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: prompt,
      config: {
        systemInstruction: systemInstruction,
      }
    });
    
    return response.text;
  } catch (error) {
    console.error("Error generating answer:", error);
    throw new Error("Failed to generate an answer from the model.");
  }
};

/**
 * Get initial welcome message for a project
 */
export const getProjectWelcomeMessage = (project: Project): ChatMessage => {
  const welcomeContent = project.config.welcomeMessage || 
    "Hello! I'm your AI assistant. How can I help you today?";
    
  return {
    role: MessageRole.MODEL,
    content: welcomeContent,
    timestamp: Date.now(),
  };
};

/**
 * Get suggested questions for a project
 */
export const getProjectSuggestedQuestions = (project: Project): string[] => {
  return project.config.suggestedQuestions.filter(q => q.trim().length > 0);
};

/**
 * Validate if a project is ready for chat (has knowledge base)
 */
export const isProjectReadyForChat = (context: ProjectChatContext | null): boolean => {
  return context !== null && context.knowledgeChunks.length > 0;
};

/**
 * Create a chat session for a project
 */
export interface ProjectChatSession {
  projectId: string;
  context: ProjectChatContext;
  messages: ChatMessage[];
  isReady: boolean;
}

export const createProjectChatSession = async (
  projectId: string,
  project: Project
): Promise<ProjectChatSession> => {
  try {
    const context = await initializeProjectChat(projectId);
    const isReady = isProjectReadyForChat(context);
    
    const initialMessages: ChatMessage[] = [];
    
    if (isReady && context) {
      // Add welcome message
      const welcomeMessage = getProjectWelcomeMessage(project);
      initialMessages.push(welcomeMessage);
    } else {
      // Add message indicating knowledge base is not ready
      initialMessages.push({
        role: MessageRole.SYSTEM,
        content: "This chatbot is not yet ready. The knowledge base needs to be built first.",
        timestamp: Date.now(),
      });
    }
    
    return {
      projectId,
      context: context || { project, knowledgeChunks: [] },
      messages: initialMessages,
      isReady,
    };
  } catch (error) {
    console.error('Error creating chat session:', error);
    throw new Error('Failed to create chat session');
  }
};

/**
 * Handle a user message in a project chat session
 */
export const handleProjectChatMessage = async (
  session: ProjectChatSession,
  userMessage: string
): Promise<{ updatedSession: ProjectChatSession; response: string }> => {
  try {
    if (!session.isReady) {
      throw new Error('Chat session is not ready');
    }
    
    // Add user message to session
    const userChatMessage: ChatMessage = {
      role: MessageRole.USER,
      content: userMessage,
      timestamp: Date.now(),
    };
    
    // Generate response
    const response = await generateProjectResponse(
      userMessage,
      session.context,
      session.context.project.config?.systemPrompt
    );
    
    // Add bot response to session
    const botChatMessage: ChatMessage = {
      role: MessageRole.MODEL,
      content: response,
      timestamp: Date.now(),
    };
    
    // Update session with new messages
    const updatedSession: ProjectChatSession = {
      ...session,
      messages: [...session.messages, userChatMessage, botChatMessage],
    };
    
    return {
      updatedSession,
      response,
    };
  } catch (error) {
    console.error('Error handling chat message:', error);
    throw new Error('Failed to process message');
  }
};
