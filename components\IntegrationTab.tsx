import React, { useState } from 'react';
import { Project, ProjectStatus } from '../types';
import { CopyIcon, CheckIcon } from './Icons';

interface IntegrationTabProps {
  project: Project;
}

export const IntegrationTab: React.FC<IntegrationTabProps> = ({ project }) => {
  const [copiedItem, setCopiedItem] = useState<string | null>(null);

  const handleCopy = async (text: string, itemId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(itemId);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const currentDomain = window.location.origin;
  const embedCode = `<!-- eComQnA Chatbot -->
<script>
  window.eComQnAConfig = {
    projectId: '${project.id}',
    themeColor: '${project.config.themeColor}',
    position: 'bottom-right',
    baseUrl: '${currentDomain}'
  };
</script>
<script src="${currentDomain}/embed.js" async></script>`;

  const iframeCode = `<iframe
  src="${currentDomain}/#/chat/${project.id}"
  width="400"
  height="600"
  frameborder="0"
  style="border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
</iframe>`;

  const testUrl = `${currentDomain}/#/chat/${project.id}`;

  const isReady = project.status === ProjectStatus.READY;

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Status Check */}
      {!isReady && (
        <div className="bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-300 dark:border-yellow-700 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <p className="text-yellow-700 dark:text-yellow-400 font-medium">
              Knowledge Base Required
            </p>
          </div>
          <p className="text-yellow-700 dark:text-yellow-400 text-sm mt-1">
            Please build your knowledge base first before integrating the chatbot.
          </p>
        </div>
      )}

      {/* Embed Code Generator */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Website Integration
        </h3>
        
        <div className="space-y-6">
          {/* Floating Widget */}
          <div>
            <h4 className="font-medium text-text-light dark:text-slate-200 mb-2">
              Floating Chat Widget (Recommended)
            </h4>
            <p className="text-sm text-text-secondary-light dark:text-slate-400 mb-3">
              Add this code to your website's HTML, just before the closing &lt;/body&gt; tag.
            </p>
            
            <div className="relative">
              <pre className="bg-slate-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
                <code>{embedCode}</code>
              </pre>
              <button
                onClick={() => handleCopy(embedCode, 'embed')}
                disabled={!isReady}
                className="absolute top-2 right-2 p-2 bg-slate-700 hover:bg-slate-600 rounded text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Copy embed code"
              >
                {copiedItem === 'embed' ? (
                  <CheckIcon className="w-4 h-4" />
                ) : (
                  <CopyIcon className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          {/* Iframe Integration */}
          <div>
            <h4 className="font-medium text-text-light dark:text-slate-200 mb-2">
              Iframe Integration
            </h4>
            <p className="text-sm text-text-secondary-light dark:text-slate-400 mb-3">
              Embed the chat directly in your page using an iframe.
            </p>
            
            <div className="relative">
              <pre className="bg-slate-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
                <code>{iframeCode}</code>
              </pre>
              <button
                onClick={() => handleCopy(iframeCode, 'iframe')}
                disabled={!isReady}
                className="absolute top-2 right-2 p-2 bg-slate-700 hover:bg-slate-600 rounded text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Copy iframe code"
              >
                {copiedItem === 'iframe' ? (
                  <CheckIcon className="w-4 h-4" />
                ) : (
                  <CopyIcon className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Testing Tools */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Testing Tools
        </h3>
        
        <div className="space-y-4">
          {/* Direct Link */}
          <div>
            <h4 className="font-medium text-text-light dark:text-slate-200 mb-2">
              Direct Chat Link
            </h4>
            <p className="text-sm text-text-secondary-light dark:text-slate-400 mb-3">
              Test your chatbot directly in a new window.
            </p>
            
            <div className="flex gap-3">
              <input
                type="text"
                value={testUrl}
                readOnly
                className="flex-1 px-3 py-2 bg-secondary-light dark:bg-slate-700 border border-border-light dark:border-slate-600 rounded-lg text-text-light dark:text-slate-200 text-sm"
              />
              <button
                onClick={() => handleCopy(testUrl, 'url')}
                disabled={!isReady}
                className="px-3 py-2 bg-secondary-light dark:bg-slate-700 border border-border-light dark:border-slate-600 rounded-lg hover:bg-border-light dark:hover:bg-slate-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Copy URL"
              >
                {copiedItem === 'url' ? (
                  <CheckIcon className="w-4 h-4" />
                ) : (
                  <CopyIcon className="w-4 h-4" />
                )}
              </button>
              <button
                onClick={() => window.open(testUrl, '_blank')}
                disabled={!isReady}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Open Test Chat
              </button>
            </div>
          </div>

          {/* QR Code */}
          <div>
            <h4 className="font-medium text-text-light dark:text-slate-200 mb-2">
              Mobile Testing
            </h4>
            <p className="text-sm text-text-secondary-light dark:text-slate-400 mb-3">
              Scan this QR code to test on mobile devices.
            </p>
            
            <div className="w-32 h-32 bg-secondary-light dark:bg-slate-700 rounded-lg flex items-center justify-center">
              <span className="text-text-secondary-light dark:text-slate-400 text-sm">
                QR Code
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Integration Instructions */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Integration Instructions
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
              1
            </div>
            <div>
              <h4 className="font-medium text-text-light dark:text-slate-200">
                Copy the Integration Code
              </h4>
              <p className="text-sm text-text-secondary-light dark:text-slate-400">
                Choose between the floating widget or iframe integration based on your needs.
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
              2
            </div>
            <div>
              <h4 className="font-medium text-text-light dark:text-slate-200">
                Add to Your Website
              </h4>
              <p className="text-sm text-text-secondary-light dark:text-slate-400">
                Paste the code into your website's HTML. For the floating widget, add it before the closing &lt;/body&gt; tag.
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
              3
            </div>
            <div>
              <h4 className="font-medium text-text-light dark:text-slate-200">
                Test and Deploy
              </h4>
              <p className="text-sm text-text-secondary-light dark:text-slate-400">
                Use the testing tools above to verify everything works correctly before going live.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Support */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2">
          Need Help?
        </h4>
        <p className="text-blue-800 dark:text-blue-300 text-sm">
          Check our documentation or contact support if you need assistance with the integration.
        </p>
      </div>
    </div>
  );
};
