import React, { useEffect, useRef, useState } from 'react';
import { ChatMessage, MessageRole } from '../types';
import { BotIcon, UserIcon, CopyIcon, CheckIcon } from './Icons';
import { marked } from 'marked';

// Configure marked for better output
// FIX: Removed deprecated 'mangle' option.
// FIX: Removed deprecated `headerIds` option. It is no longer supported in recent versions of marked.
marked.setOptions({
  gfm: true,
  breaks: true,
});

const MarkdownRenderer: React.FC<{ content: string, isUser: boolean }> = ({ content, isUser }) => {
  const html = marked.parse(content);
  // Custom class for prose to style markdown from dark background
  const proseClass = isUser 
    ? "prose prose-sm max-w-none prose-p:text-white prose-strong:text-white prose-blockquote:text-white/80"
    : "prose prose-sm max-w-none prose-p:text-text-inverted prose-strong:text-text-inverted prose-blockquote:text-text-inverted/80";

  return <div className={proseClass} dangerouslySetInnerHTML={{ __html: html as string }} />;
};

const Message: React.FC<{ message: ChatMessage }> = ({ message }) => {
  const isUser = message.role === MessageRole.USER;
  const isSystem = message.role === MessageRole.SYSTEM;
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = () => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(message.content).then(() => {
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      }).catch(err => {
        console.error('Failed to copy text: ', err);
      });
    }
  };

  if (isSystem) {
    return (
      <div className="text-center my-4 text-xs text-text-secondary">
        <span className="px-3 py-1 bg-panel-dark/50 rounded-full">
          {message.content}
        </span>
      </div>
    );
  }

  return (
    <div className={`flex items-start gap-3 my-4 ${isUser ? 'flex-row-reverse' : ''}`}>
      <div
        className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isUser ? 'bg-primary' : 'bg-slate-500'
        }`}
      >
        {isUser ? <UserIcon className="w-5 h-5 text-white" /> : <BotIcon className="w-5 h-5 text-text-inverted" />}
      </div>
      <div
        className={`relative group p-3 rounded-lg max-w-xl shadow-md ${
          isUser
            ? 'bg-primary text-white rounded-br-none'
            : 'bg-slate-700 text-text-inverted rounded-bl-none'
        }`}
      >
        <MarkdownRenderer content={message.content} isUser={isUser} />
        {!isUser && !isSystem && (
          <button
            onClick={handleCopy}
            className="absolute -top-2 -right-2 p-1 rounded-full bg-slate-600 text-slate-300 hover:bg-slate-500 transition-opacity opacity-0 group-hover:opacity-100 focus:opacity-100"
            aria-label="Copy message"
          >
            {isCopied ? (
              <CheckIcon className="w-4 h-4 text-green-400" />
            ) : (
              <CopyIcon className="w-4 h-4" />
            )}
          </button>
        )}
      </div>
    </div>
  );
};

const LoadingMessage: React.FC = () => (
  <div className="flex items-start gap-3 my-4">
    <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-slate-500">
      <BotIcon className="w-5 h-5 text-text-inverted" />
    </div>
    <div className="p-3 rounded-lg bg-slate-700 text-text-inverted rounded-bl-none shadow-md">
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-slate-500 rounded-full animate-pulse-fast"></div>
        <div className="w-2 h-2 bg-slate-500 rounded-full animate-pulse-fast" style={{ animationDelay: '0.2s' }}></div>
        <div className="w-2 h-2 bg-slate-500 rounded-full animate-pulse-fast" style={{ animationDelay: '0.4s' }}></div>
      </div>
    </div>
  </div>
);

interface ChatWindowProps {
  messages: ChatMessage[];
  isLoading: boolean;
}

export const ChatWindow: React.FC<ChatWindowProps> = ({ messages, isLoading }) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages, isLoading]);

  return (
    <div ref={scrollRef} className="flex-grow p-4 overflow-y-auto dark-scrollbar">
      {messages.map((msg) => (
        <Message key={msg.timestamp} message={msg} />
      ))}
      {isLoading && <LoadingMessage />}
    </div>
  );
};
