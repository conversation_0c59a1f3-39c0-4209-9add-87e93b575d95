
import React from 'react';
import { VectorData } from '../types';

interface ChunkVisualizerProps {
  chunks: VectorData[];
}

export const ChunkVisualizer: React.FC<ChunkVisualizerProps> = ({ chunks }) => {
  return (
    <div className="flex flex-col h-full">
      <h3 className="text-lg font-semibold text-text-primary dark:text-slate-200 mb-3">
        Knowledge Chunks ({chunks.length})
      </h3>
      <div className="flex-grow overflow-y-auto bg-secondary dark:bg-slate-900/50 p-3 rounded-lg border border-border-light dark:border-slate-700 space-y-3">
        {chunks.map((chunk, index) => (
          <div key={index} className="p-3 bg-panel dark:bg-slate-700/50 rounded-md shadow-sm border border-border-light dark:border-slate-600">
            <p className="font-mono text-xs font-semibold text-primary dark:text-indigo-400 mb-2 pb-1 border-b border-border-light dark:border-slate-600">
              CHUNK #{index + 1}
            </p>
            <p className="text-xs text-text-secondary dark:text-slate-400 leading-relaxed font-mono">
              {chunk.text}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};