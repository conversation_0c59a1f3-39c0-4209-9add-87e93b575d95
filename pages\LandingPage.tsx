import React from 'react';
import { LogoIcon, SparklesIcon, DatabaseIcon, BoltIcon, SunIcon, MoonIcon } from '../components/Icons';
import { useAuth } from '../hooks/useAuth';

const ThemeToggleButton: React.FC<{ className?: string }> = ({ className }) => {
    const { theme, toggleTheme } = useAuth();
    return (
        <button
            onClick={toggleTheme}
            className={`p-2 rounded-full text-text-secondary-light dark:text-text-secondary-dark hover:bg-secondary-light dark:hover:bg-surface-dark transition-colors ${className}`}
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
        >
            {theme === 'light' ? <MoonIcon className="w-5 h-5" /> : <SunIcon className="w-5 h-5" />}
        </button>
    );
};


const GradientIcon: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-gradient-to-br from-accent-pink/20 to-accent-cyan/20 text-accent-cyan mb-4">
        {children}
    </div>
);

const FeatureCard: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode }> = ({ icon, title, children }) => (
  <div className="relative group bg-surface-light/50 dark:bg-surface-dark/50 backdrop-blur-md p-6 rounded-xl shadow-lg border border-border-glass-light dark:border-border-glass-dark overflow-hidden transition-all duration-300 hover:border-accent-cyan/50 hover:shadow-2xl hover:shadow-accent-cyan/10">
    <GradientIcon>{icon}</GradientIcon>
    <h3 className="text-lg font-semibold text-text-light dark:text-text-dark mb-2">{title}</h3>
    <p className="text-text-secondary-light dark:text-text-secondary-dark text-sm">{children}</p>
    <div className="absolute top-0 left-0 h-full w-full bg-gradient-to-br from-accent-cyan/10 via-transparent to-accent-pink/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
  </div>
);

export const LandingPage: React.FC = () => {
    const navigateTo = (e: React.MouseEvent<HTMLAnchorElement>, hash: string) => {
        e.preventDefault();
        window.location.hash = hash;
    };

  return (
    <div className="min-h-screen bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark font-sans aurora-background transition-colors duration-300">
      <div className="grid-overlay"></div>
      <div className="relative z-10">
        <header className="py-4 px-4 sm:px-6 lg:px-8 sticky top-0 z-50 bg-background-light/50 dark:bg-background-dark/50 backdrop-blur-md border-b border-border-light dark:border-border-glass-dark">
          <nav className="flex items-center justify-between">
            <a href="#/" onClick={(e) => navigateTo(e, '#/')} className="flex items-center gap-3 group">
              <LogoIcon className="h-8 w-8 text-accent-cyan group-hover:text-primary transition-colors duration-300 [filter:drop-shadow(0_0_5px_#22D3EE)]" />
              <span className="font-bold text-lg text-text-light dark:text-text-dark group-hover:text-primary dark:group-hover:text-white transition-colors duration-300">eComQnA</span>
            </a>
            <div className="flex items-center gap-2">
              <ThemeToggleButton />
              <a href="#/signin" onClick={(e) => navigateTo(e, '#/signin')} className="px-4 py-2 text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark hover:text-text-light dark:hover:text-text-dark rounded-md transition-colors">
                Sign In
              </a>
              <a href="#/signup" onClick={(e) => navigateTo(e, '#/signup')} className="relative group inline-block px-5 py-2 text-sm font-medium text-text-light dark:text-white bg-surface-light dark:bg-surface-dark border border-border-light dark:border-border-glass-dark rounded-lg overflow-hidden transition-all duration-300 hover:border-accent-cyan">
                <span className="relative z-10">Get Started</span>
                <div className="absolute inset-0 bg-gradient-to-r from-accent-pink to-accent-cyan opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </a>
            </div>
          </nav>
        </header>

        <main>
          {/* Hero Section */}
          <section className="text-center py-24 sm:py-32 px-4 sm:px-6 lg:px-8">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-gray-400 tracking-tight leading-tight opacity-0 animate-fade-in-up" style={{ animationFillMode: 'forwards' }}>
              Automate 90% of Your <br/> E-commerce Support.
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-lg text-text-secondary-light dark:text-text-secondary-dark opacity-0 animate-fade-in-up" style={{ animationDelay: '200ms', animationFillMode: 'forwards' }}>
             Slash support tickets and boost sales with a 24/7 AI assistant trained on your products. Answer questions, guide purchases, and deliver exceptional customer experiences, automatically.
            </p>
            <div className="mt-10 flex justify-center opacity-0 animate-fade-in-up" style={{ animationDelay: '400ms', animationFillMode: 'forwards' }}>
              <a href="#/signup" onClick={(e) => navigateTo(e, '#/signup')} 
                 className="relative group inline-flex items-center justify-center px-8 py-3 font-semibold text-white bg-gradient-to-r from-accent-pink to-accent-cyan rounded-lg shadow-lg transition-transform duration-300 hover:scale-105 overflow-hidden">
                <span className="absolute h-full w-full bg-white/20 blur-2xl animate-shimmer group-hover:opacity-0 transition-opacity duration-500"></span>
                <span className="relative">Get Started Free</span>
              </a>
            </div>
          </section>

          {/* Features Section */}
          <section className="py-20">
            <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold text-text-light dark:text-text-dark">A Game-Changer for Your Online Store</h2>
                <p className="mt-3 text-text-secondary-light dark:text-text-secondary-dark">Built to handle customer queries, drive conversions, and integrate effortlessly.</p>
              </div>
              <div className="grid md:grid-cols-3 gap-8">
                <FeatureCard icon={<SparklesIcon className="w-6 h-6" />} title="Increase Conversions">
                  Engage customers with instant, accurate answers about product details, shipping, and returns. Guide them from browsing to buying, 24/7.
                </FeatureCard>
                <FeatureCard icon={<DatabaseIcon className="w-6 h-6" />} title="Reduce Support Costs">
                  Simply provide your store's FAQ page, product data, or help docs. Our AI instantly learns your business to provide accurate, on-brand answers.
                </FeatureCard>
                <FeatureCard icon={<BoltIcon className="w-6 h-6" />} title="Build Customer Trust">
                 Deliver instant, reliable information that prevents cart abandonment and builds brand loyalty. No more "I'll get back to you".
                </FeatureCard>
              </div>
            </div>
          </section>
        </main>

        <footer className="py-8 border-t border-border-light dark:border-border-glass-dark mt-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-sm text-text-secondary-light dark:text-text-secondary-dark">
            <p>&copy; {new Date().getFullYear()} eComQnA. All rights reserved.</p>
          </div>
        </footer>
      </div>
    </div>
  );
};