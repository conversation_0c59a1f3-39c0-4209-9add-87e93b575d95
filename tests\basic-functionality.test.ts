/**
 * Basic functionality tests for the project-based chatbot management system
 * These tests verify core functionality without requiring a full test environment
 */

import { describe, it, expect, vi } from 'vitest';
import { 
  validateProjectName, 
  validateWebsiteUrl,
  processFile 
} from '../services/projectService';
import { ProjectStatus, SourceType } from '../types';

describe('Project Service Validation', () => {
  describe('validateProjectName', () => {
    it('should accept valid project names', () => {
      expect(validateProjectName('My Project')).toBeNull();
      expect(validateProjectName('Test123')).toBeNull();
      expect(validateProjectName('A')).toBeNull();
    });

    it('should reject empty project names', () => {
      expect(validateProjectName('')).toBe('Project name is required');
      expect(validateProjectName('   ')).toBe('Project name is required');
    });

    it('should reject project names that are too long', () => {
      const longName = 'a'.repeat(51);
      expect(validateProjectName(longName)).toBe('Project name must be 50 characters or less');
    });
  });

  describe('validateWebsiteUrl', () => {
    it('should accept valid URLs', () => {
      expect(validateWebsiteUrl('https://example.com')).toBeNull();
      expect(validateWebsiteUrl('http://localhost:3000')).toBeNull();
      expect(validateWebsiteUrl('https://subdomain.example.com/path')).toBeNull();
    });

    it('should accept empty URLs (optional field)', () => {
      expect(validateWebsiteUrl('')).toBeNull();
      expect(validateWebsiteUrl('   ')).toBeNull();
    });

    it('should reject invalid URLs', () => {
      expect(validateWebsiteUrl('not-a-url')).toBe('Please enter a valid URL');
      expect(validateWebsiteUrl('ftp://example.com')).toBe('Please enter a valid URL');
      expect(validateWebsiteUrl('javascript:alert(1)')).toBe('Please enter a valid URL');
    });
  });
});

describe('File Processing', () => {
  it('should process text files correctly', async () => {
    const textFile = new File(['Hello world!'], 'test.txt', { type: 'text/plain' });
    const result = await processFile(textFile);
    expect(result).toBe('Hello world!');
  });

  it('should process markdown files correctly', async () => {
    const markdownContent = `# Header\n\n**Bold text** and *italic text*\n\n- List item\n- Another item`;
    const markdownFile = new File([markdownContent], 'test.md', { type: 'text/markdown' });
    const result = await processFile(markdownFile);
    
    // Should remove markdown syntax
    expect(result).not.toContain('#');
    expect(result).not.toContain('**');
    expect(result).not.toContain('*');
    expect(result).not.toContain('-');
    expect(result).toContain('Header');
    expect(result).toContain('Bold text');
    expect(result).toContain('italic text');
  });

  it('should reject unsupported file types', async () => {
    const pdfFile = new File(['fake pdf content'], 'test.pdf', { type: 'application/pdf' });
    await expect(processFile(pdfFile)).rejects.toThrow('PDF processing is not yet supported');
    
    const imageFile = new File(['fake image'], 'test.jpg', { type: 'image/jpeg' });
    await expect(processFile(imageFile)).rejects.toThrow('Unsupported file type');
  });
});

describe('Type Definitions', () => {
  it('should have correct ProjectStatus enum values', () => {
    expect(ProjectStatus.INACTIVE).toBe('inactive');
    expect(ProjectStatus.READY).toBe('ready');
  });

  it('should have correct SourceType enum values', () => {
    expect(SourceType.URL).toBe('url');
    expect(SourceType.TEXT).toBe('text');
    expect(SourceType.FILE).toBe('file');
  });
});

describe('Configuration Validation', () => {
  it('should validate theme colors', () => {
    const validColors = ['#ff0000', '#00ff00', '#0000ff', '#4f46e5'];
    validColors.forEach(color => {
      expect(color).toMatch(/^#[0-9a-fA-F]{6}$/);
    });
  });

  it('should validate system prompts', () => {
    const validPrompt = 'You are a helpful assistant.';
    expect(validPrompt.length).toBeGreaterThan(0);
    expect(validPrompt.length).toBeLessThan(2000); // Reasonable limit
  });
});

// Integration test helpers
export const createMockProject = () => ({
  id: 'test-project-id',
  ownerId: 'test-user-id',
  name: 'Test Project',
  status: ProjectStatus.INACTIVE,
  config: {
    themeColor: '#4f46e5',
    welcomeMessage: 'Hello! How can I help you?',
    systemPrompt: 'You are a helpful assistant.',
    suggestedQuestions: ['What are your hours?', 'How can I contact support?'],
  },
  knowledgeBaseStats: {
    chunkCount: 0,
  },
  createdAt: new Date(),
  updatedAt: new Date(),
});

export const createMockKnowledgeChunk = () => ({
  id: 'test-chunk-id',
  projectId: 'test-project-id',
  content: 'This is test content for the knowledge base.',
  embedding: new Array(768).fill(0.1), // Mock embedding vector
  sourceType: SourceType.TEXT,
  sourceReference: 'Test source',
  createdAt: new Date(),
});

// Test utilities
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const mockFetch = (response: any, ok = true) => {
  global.fetch = vi.fn(() =>
    Promise.resolve({
      ok,
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(typeof response === 'string' ? response : JSON.stringify(response)),
    })
  );
};
