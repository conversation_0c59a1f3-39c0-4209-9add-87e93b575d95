import { User as FirebaseUser } from 'firebase/auth';
import { User } from '../types';
import { createUser, getUser, updateUser } from './firestoreService';

export const ensureUserDocument = async (firebaseUser: FirebaseUser): Promise<User> => {
  try {
    // Check if user document already exists
    let user = await getUser(firebaseUser.uid);
    
    if (!user) {
      // Create new user document
      const now = new Date();
      const userData: Omit<User, 'id'> = {
        email: firebaseUser.email || '',
        createdAt: now,
        updatedAt: now,
      };

      // Only add displayName if it exists (avoid undefined values)
      if (firebaseUser.displayName) {
        userData.displayName = firebaseUser.displayName;
      }
      
      await createUser(firebaseUser.uid, userData);
      
      // Fetch the created user
      user = await getUser(firebaseUser.uid);
      if (!user) {
        throw new Error('Failed to create user document');
      }
    } else {
      // Update user info if it has changed
      const updates: Partial<Omit<User, 'id' | 'createdAt'>> = {};
      let hasUpdates = false;
      
      if (user.email !== firebaseUser.email && firebaseUser.email) {
        updates.email = firebaseUser.email;
        hasUpdates = true;
      }
      
      if (user.displayName !== firebaseUser.displayName) {
        // Only set displayName if it exists, otherwise omit the field
        if (firebaseUser.displayName) {
          updates.displayName = firebaseUser.displayName;
        } else if (user.displayName) {
          // If user had a displayName but Firebase user doesn't, we need to remove it
          // Firestore doesn't support setting to undefined, so we'll use null
          updates.displayName = null as any;
        }
        hasUpdates = true;
      }
      
      if (hasUpdates) {
        await updateUser(firebaseUser.uid, updates);
        // Fetch updated user
        user = await getUser(firebaseUser.uid);
        if (!user) {
          throw new Error('Failed to fetch updated user');
        }
      }
    }
    
    return user;
  } catch (error) {
    console.error('Error ensuring user document:', error);
    throw new Error('Failed to manage user document');
  }
};

export const updateUserProfile = async (
  userId: string,
  updates: { displayName?: string }
): Promise<void> => {
  try {
    // Sanitize updates to avoid undefined values
    const sanitizedUpdates: Partial<Omit<User, 'id' | 'createdAt'>> = {};

    if (updates.displayName !== undefined) {
      if (updates.displayName) {
        sanitizedUpdates.displayName = updates.displayName;
      } else {
        // If displayName is explicitly set to empty string or null, use null
        sanitizedUpdates.displayName = null as any;
      }
    }

    await updateUser(userId, sanitizedUpdates);
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw new Error('Failed to update user profile');
  }
};
