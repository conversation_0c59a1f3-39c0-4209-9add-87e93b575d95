import React, { useState } from 'react';
import { SendIcon } from './Icons';

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  disabled: boolean;
}

export const MessageInput: React.FC<MessageInputProps> = ({ onSendMessage, disabled }) => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  return (
    <div className="flex-shrink-0 p-4 bg-slate-800/50 backdrop-blur-sm border-t border-border-dark">
      <form onSubmit={handleSubmit} className="flex items-center gap-3">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder={disabled ? "Create a knowledge base to begin..." : "Ask a question..."}
          disabled={disabled}
          className="flex-grow px-4 py-2 bg-slate-700 border border-border-dark rounded-full focus:ring-2 focus:ring-primary focus:outline-none transition-shadow duration-200 text-text-inverted placeholder:text-text-secondary"
        />
        <button
          type="submit"
          disabled={disabled || !message.trim()}
          className="flex-shrink-0 w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-primary-hover transition-colors duration-200 disabled:bg-slate-600 disabled:cursor-not-allowed"
        >
          <SendIcon className="w-5 h-5" />
        </button>
      </form>
    </div>
  );
};