# Firestore Security Rules Configuration

## Critical: Update Your Firestore Security Rules

The authentication errors you're experiencing are likely due to restrictive Firestore security rules. You need to update your Firestore security rules in the Firebase Console.

## How to Update Firestore Security Rules

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `ecomqna`
3. Navigate to **Firestore Database** in the left sidebar
4. Click on the **Rules** tab
5. Replace the existing rules with the rules below
6. Click **Publish** to apply the changes

## Required Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read and write projects they own
    match /projects/{projectId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.ownerId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.ownerId;
      
      // Users can read and write chunks within their own projects
      match /chunks/{chunkId} {
        allow read, write: if request.auth != null && 
          request.auth.uid == get(/databases/$(database)/documents/projects/$(projectId)).data.ownerId;
      }
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

## What These Rules Do

1. **User Documents**: Users can only access their own user document (identified by their Firebase Auth UID)
2. **Project Documents**: Users can only access projects they own
3. **Knowledge Chunks**: Users can only access chunks within projects they own
4. **Default Deny**: All other access is denied for security

## Testing the Rules

After updating the rules:

1. Clear your browser cache and cookies for localhost
2. Restart your development server: `npm run dev`
3. Try logging in again
4. Check the browser console for any remaining errors

## Common Issues

- **Still getting permission errors?** Make sure you published the rules and wait a few minutes for them to propagate
- **Rules not working?** Double-check that the `ownerId` field in your projects matches the authenticated user's UID
- **Need to debug?** You can use the Firebase Console's Rules Playground to test specific scenarios

## Alternative: Temporary Development Rules (NOT for production)

If you need to quickly test the application, you can temporarily use these permissive rules (ONLY for development):

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

**⚠️ WARNING**: These rules allow any authenticated user to read/write any document. Only use for testing and replace with the proper rules above before going to production.
